"""
AData Adapter for Chinese Stock Market Data
Provides integration with AData library for A-share market analysis
"""

import datetime
import pandas as pd
from typing import List, Optional, Dict, Any
from src.data.models import Price, FinancialMetrics
from src.data.cache import get_cache

# Global cache instance
_cache = get_cache()

# AData integration
try:
    import adata
    ADATA_AVAILABLE = True
    print("✅ AData is available for Chinese stock data")
except ImportError:
    ADATA_AVAILABLE = False
    print("❌ AData not installed. Please install with: pip install adata")


def is_chinese_stock(ticker: str) -> bool:
    """
    Check if a ticker is a Chinese A-share stock
    Chinese stocks are typically 6 digits: 000001, 600001, 300001, etc.
    """
    if not ticker or len(ticker) != 6:
        return False
    
    # Check if it's all digits
    if not ticker.isdigit():
        return False
    
    # Check common A-share prefixes
    prefixes = ['000', '001', '002', '003', '300', '301', '600', '601', '603', '688']
    return any(ticker.startswith(prefix) for prefix in prefixes)


def get_adata_prices(ticker: str, start_date: str, end_date: str) -> List[Price]:
    """
    Fetch price data using AData for Chinese stocks
    """
    if not ADATA_AVAILABLE:
        raise Exception("AData is not available. Please install with: pip install adata")
    
    if not is_chinese_stock(ticker):
        raise Exception(f"Ticker {ticker} is not a Chinese A-share stock")
    
    # Create cache key
    cache_key = f"adata_{ticker}_{start_date}_{end_date}"
    
    # Check cache first
    if cached_data := _cache.get_prices(cache_key):
        return [Price(**price) for price in cached_data]
    
    try:
        # Fetch data from AData
        df = adata.stock.market.get_market(
            stock_code=ticker,
            start_date=start_date,
            end_date=end_date,
            k_type=1,  # Daily K-line
            adjust_type=1  # Forward adjusted
        )
        
        if df.empty:
            return []
        
        # Convert to Price objects
        prices = []
        for _, row in df.iterrows():
            # Handle date conversion properly
            trade_date = row['trade_date']
            if isinstance(trade_date, str):
                time_str = trade_date
            else:
                time_str = trade_date.strftime('%Y-%m-%d')

            price = Price(
                open=float(row['open']),
                close=float(row['close']),
                high=float(row['high']),
                low=float(row['low']),
                volume=int(row['volume']),
                time=time_str
            )
            prices.append(price)
        
        # Cache the results
        _cache.set_prices(cache_key, [p.model_dump() for p in prices])
        return prices
        
    except Exception as e:
        raise Exception(f"Error fetching AData prices for {ticker}: {str(e)}")


def get_adata_financial_metrics(ticker: str, end_date: str, period: str = "ttm", limit: int = 10) -> List[FinancialMetrics]:
    """
    Fetch financial metrics using AData for Chinese stocks
    """
    if not ADATA_AVAILABLE:
        raise Exception("AData is not available. Please install with: pip install adata")
    
    if not is_chinese_stock(ticker):
        raise Exception(f"Ticker {ticker} is not a Chinese A-share stock")
    
    # Create cache key
    cache_key = f"adata_metrics_{ticker}_{period}_{end_date}_{limit}"
    
    # Check cache first
    if cached_data := _cache.get_financial_metrics(cache_key):
        return [FinancialMetrics(**metric) for metric in cached_data]
    
    try:
        # Fetch financial data from AData
        df = adata.stock.finance.get_core_index(stock_code=ticker)
        
        if df.empty:
            return []
        
        # Convert to FinancialMetrics objects
        metrics = []
        for _, row in df.iterrows():
            # Handle report date conversion properly
            report_date = row.get('report_date')
            if pd.notna(report_date):
                if isinstance(report_date, str):
                    report_period = report_date
                else:
                    report_period = report_date.strftime('%Y-%m-%d')
            else:
                report_period = ''

            # Map AData fields to our FinancialMetrics model
            metric = FinancialMetrics(
                ticker=ticker,
                report_period=report_period,
                period=period,
                currency='CNY',  # Chinese Yuan
                market_cap=None,  # Will be calculated separately
                enterprise_value=None,
                price_to_earnings_ratio=None,  # Not directly available in AData
                price_to_book_ratio=None,
                price_to_sales_ratio=None,
                enterprise_value_to_ebitda_ratio=None,
                enterprise_value_to_revenue_ratio=None,
                free_cash_flow_yield=None,
                peg_ratio=None,
                gross_margin=float(row['gross_margin']) if pd.notna(row.get('gross_margin')) else None,
                operating_margin=None,
                net_margin=float(row['net_margin']) if pd.notna(row.get('net_margin')) else None,
                return_on_equity=float(row['roe_wtd']) if pd.notna(row.get('roe_wtd')) else None,
                return_on_assets=float(row['roa_wtd']) if pd.notna(row.get('roa_wtd')) else None,
                return_on_invested_capital=None,
                asset_turnover=float(row['total_asset_turn_rate']) if pd.notna(row.get('total_asset_turn_rate')) else None,
                inventory_turnover=float(row['inv_turn_rate']) if pd.notna(row.get('inv_turn_rate')) else None,
                receivables_turnover=float(row['acct_recv_turn_rate']) if pd.notna(row.get('acct_recv_turn_rate')) else None,
                days_sales_outstanding=float(row['acct_recv_turn_days']) if pd.notna(row.get('acct_recv_turn_days')) else None,
                operating_cycle=None,
                working_capital_turnover=None,
                current_ratio=float(row['curr_ratio']) if pd.notna(row.get('curr_ratio')) else None,
                quick_ratio=float(row['quick_ratio']) if pd.notna(row.get('quick_ratio')) else None,
                cash_ratio=None,
                operating_cash_flow_ratio=float(row['cash_flow_ratio']) if pd.notna(row.get('cash_flow_ratio')) else None,
                debt_to_equity=None,
                debt_to_assets=float(row['asset_liab_ratio']) if pd.notna(row.get('asset_liab_ratio')) else None,
                interest_coverage=None,
                debt_to_capital=None,
                debt_to_ebitda=None,
                long_term_debt_to_capital=None,
                long_term_debt_to_equity=None,
                capex_to_operating_cash_flow=None,
                capex_to_revenue=None,
                capex_to_depreciation=None,
                effective_tax_rate=float(row['eff_tax_rate']) if pd.notna(row.get('eff_tax_rate')) else None,
                free_cash_flow_to_revenue=None,
                free_cash_flow_to_operating_cash_flow=None,
                free_cash_flow_to_sales=None,
                operating_cash_flow_to_sales=None,
                capex_as_percent_of_sales=None,
                capex_as_percent_of_revenue=None,
                revenue_growth=float(row['total_rev_yoy_gr']) if pd.notna(row.get('total_rev_yoy_gr')) else None,
                earnings_growth=float(row['net_profit_yoy_gr']) if pd.notna(row.get('net_profit_yoy_gr')) else None,
                book_value_growth=None,
                earnings_per_share_growth=None,
                free_cash_flow_growth=None,
                operating_income_growth=None,
                ebitda_growth=None,
                payout_ratio=None,
                earnings_per_share=float(row['diluted_eps']) if pd.notna(row.get('diluted_eps')) else None,
                book_value_per_share=float(row['net_asset_ps']) if pd.notna(row.get('net_asset_ps')) else None,
                free_cash_flow_per_share=float(row['oper_cf_ps']) if pd.notna(row.get('oper_cf_ps')) else None
            )
            metrics.append(metric)
        
        # Cache the results
        _cache.set_financial_metrics(cache_key, [m.model_dump() for m in metrics])
        return metrics[:limit]
        
    except Exception as e:
        raise Exception(f"Error fetching AData financial metrics for {ticker}: {str(e)}")


def get_adata_market_cap(ticker: str, end_date: str) -> Optional[float]:
    """
    Calculate market cap using AData stock shares and current price
    """
    if not ADATA_AVAILABLE:
        raise Exception("AData is not available. Please install with: pip install adata")
    
    if not is_chinese_stock(ticker):
        raise Exception(f"Ticker {ticker} is not a Chinese A-share stock")
    
    try:
        # Get stock shares information
        shares_df = adata.stock.info.get_stock_shares(stock_code=ticker, is_history=False)
        if shares_df.empty:
            return None
        
        # Get latest total shares
        latest_shares = shares_df.iloc[0]['total_shares']
        
        # Get current price
        current_prices = adata.stock.market.list_market_current(code_list=[ticker])
        if current_prices.empty:
            return None
        
        current_price = current_prices.iloc[0]['price']
        
        # Calculate market cap (shares * price)
        market_cap = float(latest_shares) * float(current_price)
        return market_cap
        
    except Exception as e:
        print(f"Error calculating market cap for {ticker}: {str(e)}")
        return None


def get_adata_dividend_info(ticker: str) -> List[Dict[str, Any]]:
    """
    Get dividend information using AData
    """
    if not ADATA_AVAILABLE:
        raise Exception("AData is not available. Please install with: pip install adata")
    
    if not is_chinese_stock(ticker):
        raise Exception(f"Ticker {ticker} is not a Chinese A-share stock")
    
    try:
        df = adata.stock.market.get_dividend(stock_code=ticker)
        
        if df.empty:
            return []
        
        dividends = []
        for _, row in df.iterrows():
            # Handle date conversions properly
            report_date = row.get('report_date')
            if pd.notna(report_date):
                if isinstance(report_date, str):
                    report_date_str = report_date
                else:
                    report_date_str = report_date.strftime('%Y-%m-%d')
            else:
                report_date_str = ''

            ex_dividend_date = row.get('ex_dividend_date')
            if pd.notna(ex_dividend_date):
                if isinstance(ex_dividend_date, str):
                    ex_dividend_date_str = ex_dividend_date
                else:
                    ex_dividend_date_str = ex_dividend_date.strftime('%Y-%m-%d')
            else:
                ex_dividend_date_str = ''

            dividend = {
                'ticker': ticker,
                'report_date': report_date_str,
                'ex_dividend_date': ex_dividend_date_str,
                'dividend_plan': row.get('dividend_plan', ''),
            }
            dividends.append(dividend)
        
        return dividends
        
    except Exception as e:
        raise Exception(f"Error fetching AData dividend info for {ticker}: {str(e)}")


def get_all_chinese_stocks() -> List[Dict[str, str]]:
    """
    Get all Chinese A-share stock codes
    """
    if not ADATA_AVAILABLE:
        raise Exception("AData is not available. Please install with: pip install adata")
    
    try:
        df = adata.stock.info.all_code()
        
        stocks = []
        for _, row in df.iterrows():
            # Handle list date conversion properly
            list_date = row.get('list_date')
            if pd.notna(list_date):
                if isinstance(list_date, str):
                    list_date_str = list_date
                else:
                    list_date_str = list_date.strftime('%Y-%m-%d')
            else:
                list_date_str = ''

            stock = {
                'ticker': row['stock_code'],
                'name': row['short_name'],
                'exchange': row['exchange'],
                'list_date': list_date_str
            }
            stocks.append(stock)
        
        return stocks
        
    except Exception as e:
        raise Exception(f"Error fetching Chinese stock list: {str(e)}")
