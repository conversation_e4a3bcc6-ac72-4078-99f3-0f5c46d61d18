#!/usr/bin/env python3
"""
Test AI analysis with Chinese and US stocks
Simplified test without full workflow dependencies
"""

import sys
import os
from datetime import datetime, timedelta

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from tools.api import get_prices, get_financial_metrics, get_market_cap

def simple_stock_analysis(ticker: str):
    """Perform a simple stock analysis"""
    print(f"\n🔍 Analyzing {ticker}")
    print("-" * 30)
    
    try:
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        # Get price data
        prices = get_prices(ticker, start_date, end_date)
        if not prices:
            print(f"❌ No price data for {ticker}")
            return None
        
        latest_price = prices[-1]
        first_price = prices[0]
        price_change = ((float(latest_price.close) - float(first_price.close)) / float(first_price.close)) * 100
        
        # Get financial metrics
        metrics = get_financial_metrics(ticker, end_date)
        
        # Get market cap
        market_cap = get_market_cap(ticker, end_date)
        
        # Determine currency and market
        is_chinese = ticker.isdigit() and len(ticker) == 6
        currency = "¥" if is_chinese else "$"
        market = "China A-Share" if is_chinese else "US Stock"
        
        analysis = {
            'ticker': ticker,
            'market': market,
            'currency': currency,
            'current_price': float(latest_price.close),
            'price_change_30d': price_change,
            'volume': latest_price.volume,
            'market_cap': market_cap,
            'metrics': metrics[0] if metrics else None
        }
        
        # Print analysis
        print(f"📊 {market}")
        print(f"💰 Current Price: {currency}{latest_price.close}")
        print(f"📈 30-day Change: {price_change:+.2f}%")
        print(f"📦 Volume: {latest_price.volume:,}")
        
        if market_cap:
            market_cap_billion = market_cap / 1_000_000_000
            print(f"🏢 Market Cap: {currency}{market_cap_billion:.2f}B")
        
        if metrics:
            metric = metrics[0]
            if metric.return_on_equity:
                print(f"📊 ROE: {metric.return_on_equity:.2f}%")
            if metric.return_on_assets:
                print(f"📊 ROA: {metric.return_on_assets:.2f}%")
            if metric.current_ratio:
                print(f"📊 Current Ratio: {metric.current_ratio:.2f}")
        
        return analysis
        
    except Exception as e:
        print(f"❌ Error analyzing {ticker}: {str(e)}")
        return None

def generate_simple_recommendation(analysis):
    """Generate a simple investment recommendation"""
    if not analysis:
        return "❌ Cannot analyze - insufficient data"
    
    score = 0
    reasons = []
    
    # Price momentum
    if analysis['price_change_30d'] > 5:
        score += 2
        reasons.append("Strong positive momentum (+5%)")
    elif analysis['price_change_30d'] > 0:
        score += 1
        reasons.append("Positive momentum")
    elif analysis['price_change_30d'] < -10:
        score -= 2
        reasons.append("Weak momentum (-10%)")
    
    # Financial health
    if analysis['metrics']:
        metric = analysis['metrics']
        
        # ROE analysis
        if metric.return_on_equity and metric.return_on_equity > 15:
            score += 2
            reasons.append("High ROE (>15%)")
        elif metric.return_on_equity and metric.return_on_equity > 10:
            score += 1
            reasons.append("Good ROE (>10%)")
        
        # Current ratio
        if metric.current_ratio and metric.current_ratio > 1.5:
            score += 1
            reasons.append("Strong liquidity")
        elif metric.current_ratio and metric.current_ratio < 1:
            score -= 1
            reasons.append("Liquidity concerns")
        
        # Profitability
        if metric.net_margin and metric.net_margin > 20:
            score += 1
            reasons.append("High profit margins")
    
    # Generate recommendation
    if score >= 4:
        recommendation = "🟢 STRONG BUY"
    elif score >= 2:
        recommendation = "🟡 BUY"
    elif score >= 0:
        recommendation = "🟡 HOLD"
    elif score >= -2:
        recommendation = "🟠 SELL"
    else:
        recommendation = "🔴 STRONG SELL"
    
    return f"{recommendation} (Score: {score}/6)\nReasons: {', '.join(reasons) if reasons else 'Limited data'}"

def test_mixed_portfolio():
    """Test analysis of mixed US and Chinese portfolio"""
    print("🌍 Mixed Portfolio Analysis Test")
    print("=" * 50)
    
    portfolio = [
        "AAPL",    # Apple (US)
        "000001",  # 平安银行 (China)
        "MSFT",    # Microsoft (US)
        "600036",  # 招商银行 (China)
        "000858",  # 五粮液 (China)
    ]
    
    analyses = []
    
    for ticker in portfolio:
        analysis = simple_stock_analysis(ticker)
        if analysis:
            analyses.append(analysis)
            recommendation = generate_simple_recommendation(analysis)
            print(f"💡 Recommendation: {recommendation}")
        print()
    
    # Portfolio summary
    print("📋 Portfolio Summary")
    print("-" * 20)
    
    us_stocks = [a for a in analyses if a['market'] == 'US Stock']
    chinese_stocks = [a for a in analyses if a['market'] == 'China A-Share']
    
    print(f"🇺🇸 US Stocks: {len(us_stocks)}")
    print(f"🇨🇳 Chinese Stocks: {len(chinese_stocks)}")
    
    if analyses:
        avg_change = sum(a['price_change_30d'] for a in analyses) / len(analyses)
        print(f"📊 Average 30-day Change: {avg_change:+.2f}%")
        
        total_market_cap = sum(a['market_cap'] for a in analyses if a['market_cap'])
        if total_market_cap:
            print(f"💰 Total Market Cap: ${total_market_cap/1_000_000_000:.2f}B")

def main():
    """Main test function"""
    print("🧪 AI Analysis Test with AData Integration")
    print("=" * 50)
    
    # Check AData availability
    from tools.adata_adapter import ADATA_AVAILABLE
    if ADATA_AVAILABLE:
        print("✅ AData is available - Chinese stocks supported")
    else:
        print("⚠️  AData not available - Chinese stocks may not work")
    
    print()
    
    # Run portfolio test
    test_mixed_portfolio()
    
    print("\n🎉 Test completed!")
    print("\n💡 This demonstrates that the AI hedge fund system can now:")
    print("  • Analyze both US and Chinese stocks")
    print("  • Generate investment recommendations")
    print("  • Handle mixed portfolios")
    print("  • Use AData for Chinese market data")

if __name__ == "__main__":
    main()
