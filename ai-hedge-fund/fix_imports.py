#!/usr/bin/env python3
"""
批量修复src目录下的导入问题
将 "from src." 替换为相对导入
"""

import os
import re

def fix_imports_in_file(file_path):
    """修复单个文件的导入"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换 "from src." 为相对导入
        original_content = content
        
        # 匹配 from src.xxx import yyy 的模式
        content = re.sub(r'from src\.', 'from ', content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 修复: {file_path}")
            return True
        else:
            print(f"⏭️  跳过: {file_path} (无需修复)")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {file_path} - {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始批量修复导入问题...")
    
    # 需要修复的文件列表
    files_to_fix = [
        "src/tools/adata_adapter.py",
        "src/tools/api.py", 
        "src/agents/peter_lynch.py",
        "src/agents/rakesh_jhunjhunwala.py",
        "src/agents/warren_buffett.py",
        "src/agents/michael_burry.py",
        "src/agents/sentiment.py",
        "src/agents/fundamentals.py",
        "src/agents/stanley_druckenmiller.py",
        "src/agents/technicals.py",
        "src/agents/cathie_wood.py",
        "src/agents/phil_fisher.py",
        "src/agents/bill_ackman.py",
        "src/agents/valuation.py",
        "src/agents/charlie_munger.py",
        "src/agents/ben_graham.py",
        "src/agents/aswath_damodaran.py",
        "src/utils/llm.py",
        "src/utils/analysts.py",
        "src/backtester.py"
    ]
    
    fixed_count = 0
    total_count = len(files_to_fix)
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            if fix_imports_in_file(file_path):
                fixed_count += 1
        else:
            print(f"⚠️  文件不存在: {file_path}")
    
    print(f"\n🎉 修复完成! 共处理 {total_count} 个文件，修复了 {fixed_count} 个文件")

if __name__ == "__main__":
    main()
