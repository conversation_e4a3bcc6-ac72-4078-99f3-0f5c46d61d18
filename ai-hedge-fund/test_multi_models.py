#!/usr/bin/env python3
"""
多模型测试脚本
测试不同AI模型在股票分析中的表现和选择机制
"""

import sys
import os
from datetime import datetime, timedelta

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from llm.models import get_model, get_models_list, ModelProvider, AVAILABLE_MODELS
from utils.llm import call_llm
from pydantic import BaseModel
from typing import Literal

class SimpleAnalysis(BaseModel):
    """简单的股票分析模型"""
    recommendation: Literal["buy", "sell", "hold"]
    confidence: float
    reasoning: str

def test_api_keys():
    """测试所有API密钥的可用性"""
    print("🔑 测试API密钥可用性")
    print("=" * 50)
    
    api_keys = {
        "OpenAI": "OPENAI_API_KEY",
        "Anthropic": "ANTHROPIC_API_KEY", 
        "Google": "GOOGLE_API_KEY",
        "Groq": "GROQ_API_KEY",
        "DeepSeek": "DEEPSEEK_API_KEY"
    }
    
    available_providers = []
    
    for provider, env_var in api_keys.items():
        api_key = os.getenv(env_var)
        if api_key and api_key != f"your-{provider.lower()}-api-key-here":
            print(f"✅ {provider}: API密钥已设置")
            available_providers.append(provider)
        else:
            print(f"❌ {provider}: API密钥未设置或为示例值")
    
    print(f"\n📊 可用提供商: {len(available_providers)}/{len(api_keys)}")
    return available_providers

def test_model_initialization():
    """测试模型初始化"""
    print("\n🤖 测试模型初始化")
    print("=" * 50)
    
    test_models = [
        ("gpt-4o", ModelProvider.OPENAI),
        ("claude-3-5-haiku-latest", ModelProvider.ANTHROPIC),
        ("gemini-2.5-flash-preview-05-20", ModelProvider.GEMINI),
        ("meta-llama/llama-4-scout-17b-16e-instruct", ModelProvider.GROQ),
        ("deepseek-chat", ModelProvider.DEEPSEEK)
    ]
    
    successful_models = []
    
    for model_name, provider in test_models:
        try:
            print(f"🔄 初始化 {provider.value} - {model_name}")
            model = get_model(model_name, provider)
            if model:
                print(f"✅ {provider.value} 模型初始化成功")
                successful_models.append((model_name, provider))
            else:
                print(f"❌ {provider.value} 模型初始化失败")
        except Exception as e:
            print(f"❌ {provider.value} 初始化错误: {str(e)}")
    
    print(f"\n📊 成功初始化: {len(successful_models)}/{len(test_models)} 个模型")
    return successful_models

def test_simple_llm_call(model_name: str, provider: ModelProvider):
    """测试简单的LLM调用"""
    try:
        prompt = f"""
        请对苹果公司(AAPL)进行简单的投资分析。
        
        请返回JSON格式:
        {{
            "recommendation": "buy" | "sell" | "hold",
            "confidence": 0.0-100.0,
            "reasoning": "分析原因"
        }}
        """
        
        # 模拟状态对象
        class MockState:
            def get(self, key, default=None):
                if key == "metadata":
                    return {
                        "model_name": model_name,
                        "model_provider": provider.value
                    }
                return default
        
        state = MockState()
        
        result = call_llm(
            prompt=prompt,
            pydantic_model=SimpleAnalysis,
            agent_name="test_agent",
            state=state,
            max_retries=1
        )
        
        return result
        
    except Exception as e:
        print(f"❌ LLM调用失败: {str(e)}")
        return None

def test_model_comparison():
    """对比不同模型的分析结果"""
    print("\n📊 多模型对比测试")
    print("=" * 50)
    
    # 获取可用模型
    successful_models = test_model_initialization()
    
    if not successful_models:
        print("❌ 没有可用的模型进行测试")
        return
    
    print(f"\n🎯 使用 {len(successful_models)} 个模型分析 AAPL 股票")
    print("-" * 30)
    
    results = {}
    
    for model_name, provider in successful_models:
        print(f"\n🤖 使用 {provider.value} - {model_name}")
        
        result = test_simple_llm_call(model_name, provider)
        
        if result:
            results[f"{provider.value}"] = result
            print(f"✅ 推荐: {result.recommendation}")
            print(f"📊 信心度: {result.confidence}%")
            print(f"💭 理由: {result.reasoning[:100]}...")
        else:
            print(f"❌ 分析失败")
    
    # 汇总结果
    if results:
        print(f"\n📋 分析结果汇总")
        print("-" * 20)
        
        recommendations = {}
        for provider, result in results.items():
            rec = result.recommendation
            if rec not in recommendations:
                recommendations[rec] = []
            recommendations[rec].append(provider)
        
        for rec, providers in recommendations.items():
            print(f"{rec.upper()}: {', '.join(providers)}")
        
        avg_confidence = sum(r.confidence for r in results.values()) / len(results)
        print(f"\n平均信心度: {avg_confidence:.1f}%")

def test_default_model_behavior():
    """测试默认模型行为"""
    print("\n🎯 测试默认模型行为")
    print("=" * 50)
    
    # 测试1: 无指定模型时的默认行为
    print("1️⃣ 测试默认模型选择")
    try:
        from utils.llm import call_llm
        
        class MockState:
            def get(self, key, default=None):
                return default if key != "metadata" else {}
        
        state = MockState()
        
        # 这应该使用默认的 gpt-4o
        result = call_llm(
            prompt="简单测试",
            pydantic_model=SimpleAnalysis,
            agent_name=None,
            state=state,
            max_retries=1
        )
        
        print("✅ 默认模型调用成功")
        
    except Exception as e:
        print(f"❌ 默认模型调用失败: {str(e)}")
    
    # 测试2: 显示所有可用模型
    print("\n2️⃣ 系统中配置的所有模型")
    models_list = get_models_list()
    
    for i, model in enumerate(models_list, 1):
        print(f"{i:2d}. {model['display_name']} ({model['provider']})")
    
    print(f"\n总计: {len(models_list)} 个模型配置")

def test_model_switching():
    """测试模型切换功能"""
    print("\n🔄 测试模型切换功能")
    print("=" * 50)
    
    # 模拟不同的模型配置
    test_configs = [
        ("gpt-4o", "OPENAI", "默认OpenAI模型"),
        ("claude-3-5-haiku-latest", "ANTHROPIC", "Anthropic Claude模型"),
        ("gemini-2.5-flash-preview-05-20", "GEMINI", "Google Gemini模型"),
    ]
    
    for model_name, provider_str, description in test_configs:
        print(f"\n🎯 测试 {description}")
        
        try:
            provider = ModelProvider(provider_str)
            
            # 检查API密钥
            api_key_map = {
                "OPENAI": "OPENAI_API_KEY",
                "ANTHROPIC": "ANTHROPIC_API_KEY", 
                "GEMINI": "GOOGLE_API_KEY"
            }
            
            api_key = os.getenv(api_key_map[provider_str])
            if not api_key or "your-" in api_key:
                print(f"⚠️  跳过 {description} - API密钥未设置")
                continue
            
            # 尝试初始化模型
            model = get_model(model_name, provider)
            print(f"✅ {description} 初始化成功")
            
        except Exception as e:
            print(f"❌ {description} 失败: {str(e)}")

def main():
    """主测试函数"""
    print("🚀 多模型AI系统测试")
    print("=" * 60)
    
    # 检查环境
    print("📁 检查.env文件...")
    if not os.path.exists('.env'):
        print("❌ .env文件不存在，请先创建并配置API密钥")
        return
    
    print("✅ .env文件存在")
    
    # 运行测试
    try:
        # 1. 测试API密钥
        available_providers = test_api_keys()
        
        # 2. 测试默认行为
        test_default_model_behavior()
        
        # 3. 测试模型切换
        test_model_switching()
        
        # 4. 如果有可用的API密钥，进行实际测试
        if available_providers:
            test_model_comparison()
        else:
            print("\n⚠️  没有可用的API密钥，跳过实际模型测试")
            print("请在.env文件中设置真实的API密钥以进行完整测试")
        
        print("\n🎉 测试完成!")
        print("\n💡 使用说明:")
        print("  • 设置真实API密钥后可进行完整测试")
        print("  • 使用 --model 和 --provider 参数指定模型")
        print("  • 系统默认使用 OpenAI GPT-4o")
        print("  • 可以在Web界面中动态切换模型")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")

if __name__ == "__main__":
    main()
