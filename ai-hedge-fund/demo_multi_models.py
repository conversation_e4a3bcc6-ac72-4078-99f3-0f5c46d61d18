#!/usr/bin/env python3
"""
多模型演示脚本
展示如何配置和使用4个主要AI模型提供商
"""

import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def create_sample_env_file():
    """创建示例.env文件"""
    print("📝 创建示例.env文件")
    print("=" * 50)
    
    sample_env_content = """# AI模型API密钥配置
# 请将示例密钥替换为您的真实API密钥

# OpenAI API密钥 (GPT-4o, GPT-4.1等)
# 获取地址: https://platform.openai.com/
OPENAI_API_KEY=sk-your-real-openai-api-key-here

# Anthropic API密钥 (<PERSON>系列)
# 获取地址: https://anthropic.com/
ANTHROPIC_API_KEY=sk-ant-your-real-anthropic-api-key-here

# Google API密钥 (Gemini系列)
# 获取地址: https://ai.dev/
GOOGLE_API_KEY=AIza-your-real-google-api-key-here

# Groq API密钥 (Llama系列)
# 获取地址: https://groq.com/
GROQ_API_KEY=gsk_your-real-groq-api-key-here

# DeepSeek API密钥 (DeepSeek系列)
# 获取地址: https://deepseek.com/
DEEPSEEK_API_KEY=sk-your-real-deepseek-api-key-here

# 金融数据API密钥
# 获取地址: https://financialdatasets.ai/
FINANCIAL_DATASETS_API_KEY=your-financial-datasets-api-key

# Ollama配置 (本地模型)
OLLAMA_HOST=localhost
OLLAMA_BASE_URL=http://localhost:11434
"""
    
    if not os.path.exists('.env'):
        with open('.env', 'w') as f:
            f.write(sample_env_content)
        print("✅ 已创建 .env 文件")
    else:
        print("✅ .env 文件已存在")
    
    print("\n💡 请编辑 .env 文件，将示例密钥替换为您的真实API密钥")

def show_model_selection_examples():
    """展示模型选择示例"""
    print("\n🎯 模型选择示例")
    print("=" * 50)
    
    examples = [
        {
            "scenario": "默认使用",
            "command": "python src/main.py --ticker AAPL",
            "description": "使用默认的 OpenAI GPT-4o 模型",
            "model": "gpt-4o",
            "provider": "OpenAI"
        },
        {
            "scenario": "使用Claude进行深度分析",
            "command": "python src/main.py --ticker AAPL --model claude-3-5-haiku-latest --provider Anthropic",
            "description": "使用Anthropic Claude进行详细的价值投资分析",
            "model": "claude-3-5-haiku-latest",
            "provider": "Anthropic"
        },
        {
            "scenario": "使用Gemini快速分析",
            "command": "python src/main.py --ticker AAPL --model gemini-2.5-flash-preview-05-20 --provider Gemini",
            "description": "使用Google Gemini进行快速市场分析",
            "model": "gemini-2.5-flash-preview-05-20",
            "provider": "Gemini"
        },
        {
            "scenario": "使用Groq开源模型",
            "command": "python src/main.py --ticker AAPL --model meta-llama/llama-4-scout-17b-16e-instruct --provider Groq",
            "description": "使用Groq的Llama模型进行成本效益分析",
            "model": "meta-llama/llama-4-scout-17b-16e-instruct",
            "provider": "Groq"
        },
        {
            "scenario": "使用DeepSeek推理模型",
            "command": "python src/main.py --ticker AAPL --model deepseek-reasoner --provider DeepSeek",
            "description": "使用DeepSeek R1进行逻辑推理分析",
            "model": "deepseek-reasoner",
            "provider": "DeepSeek"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}️⃣ {example['scenario']}")
        print(f"   模型: {example['model']} ({example['provider']})")
        print(f"   用途: {example['description']}")
        print(f"   命令: {example['command']}")

def show_model_comparison():
    """展示模型对比"""
    print("\n📊 模型特性对比")
    print("=" * 50)
    
    models = [
        {
            "provider": "OpenAI",
            "model": "GPT-4o",
            "strengths": "通用性强、推理能力好、JSON支持",
            "use_case": "默认选择、综合分析",
            "cost": "中等",
            "speed": "中等"
        },
        {
            "provider": "Anthropic",
            "model": "Claude 3.5",
            "strengths": "深度思考、安全性高、长文本处理",
            "use_case": "价值投资分析、风险评估",
            "cost": "较高",
            "speed": "较慢"
        },
        {
            "provider": "Google",
            "model": "Gemini 2.5",
            "strengths": "多模态、快速响应、成本效益",
            "use_case": "快速分析、图表处理",
            "cost": "较低",
            "speed": "快"
        },
        {
            "provider": "Groq",
            "model": "Llama 4",
            "strengths": "开源、高速推理、成本低",
            "use_case": "大量数据处理、成本控制",
            "cost": "低",
            "speed": "很快"
        },
        {
            "provider": "DeepSeek",
            "model": "DeepSeek R1",
            "strengths": "推理能力强、中文支持好",
            "use_case": "逻辑推理、中国市场分析",
            "cost": "低",
            "speed": "中等"
        }
    ]
    
    print(f"{'提供商':<12} {'模型':<15} {'优势':<25} {'适用场景':<20} {'成本':<8} {'速度'}")
    print("-" * 100)
    
    for model in models:
        print(f"{model['provider']:<12} {model['model']:<15} {model['strengths']:<25} {model['use_case']:<20} {model['cost']:<8} {model['speed']}")

def show_configuration_guide():
    """显示配置指南"""
    print("\n⚙️ 配置指南")
    print("=" * 50)
    
    print("1️⃣ 获取API密钥:")
    print("   • OpenAI: https://platform.openai.com/ → API Keys")
    print("   • Anthropic: https://anthropic.com/ → Console → API Keys")
    print("   • Google: https://ai.dev/ → Get API Key")
    print("   • Groq: https://groq.com/ → Console → API Keys")
    print("   • DeepSeek: https://deepseek.com/ → API Keys")
    
    print("\n2️⃣ 配置.env文件:")
    print("   • 编辑项目根目录下的 .env 文件")
    print("   • 将示例密钥替换为真实密钥")
    print("   • 保存文件并重启应用")
    
    print("\n3️⃣ 验证配置:")
    print("   • 运行: python test_models_simple.py")
    print("   • 检查API密钥是否正确配置")
    
    print("\n4️⃣ 使用模型:")
    print("   • 命令行: python src/main.py --ticker AAPL --model MODEL_NAME --provider PROVIDER")
    print("   • Web界面: cd app && ./run.sh (然后在界面中选择模型)")

def show_usage_scenarios():
    """展示使用场景"""
    print("\n🎭 使用场景建议")
    print("=" * 50)
    
    scenarios = [
        {
            "scenario": "日常股票分析",
            "recommendation": "OpenAI GPT-4o",
            "reason": "平衡的性能和成本，适合大多数分析需求"
        },
        {
            "scenario": "深度价值投资研究",
            "recommendation": "Anthropic Claude",
            "reason": "深度思考能力强，适合复杂的投资逻辑分析"
        },
        {
            "scenario": "快速市场扫描",
            "recommendation": "Google Gemini Flash",
            "reason": "响应速度快，成本低，适合大量股票筛选"
        },
        {
            "scenario": "大规模回测",
            "recommendation": "Groq Llama",
            "reason": "推理速度极快，成本最低，适合批量处理"
        },
        {
            "scenario": "中国A股分析",
            "recommendation": "DeepSeek",
            "reason": "中文理解能力强，适合中国市场特色分析"
        },
        {
            "scenario": "混合投资组合",
            "recommendation": "多模型组合",
            "reason": "不同代理使用不同模型，发挥各自优势"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📈 {scenario['scenario']}")
        print(f"   推荐: {scenario['recommendation']}")
        print(f"   原因: {scenario['reason']}")

def show_cost_optimization():
    """展示成本优化建议"""
    print("\n💰 成本优化建议")
    print("=" * 50)
    
    print("🔥 高频使用场景:")
    print("   • 使用 Groq (最便宜) 或 Gemini Flash (快速便宜)")
    print("   • 避免使用 Claude Opus (最贵)")
    
    print("\n⚡ 速度优先场景:")
    print("   • 首选 Groq Llama (推理速度最快)")
    print("   • 次选 Gemini Flash (响应快)")
    
    print("\n🎯 质量优先场景:")
    print("   • 重要决策使用 Claude Sonnet (质量最高)")
    print("   • 复杂分析使用 GPT-4o (平衡性好)")
    
    print("\n🔄 混合策略:")
    print("   • 初步筛选: Groq/Gemini (快速便宜)")
    print("   • 深度分析: Claude/GPT-4o (高质量)")
    print("   • 最终决策: 人工审核 + AI辅助")

def main():
    """主演示函数"""
    print("🤖 AI对冲基金 - 多模型配置演示")
    print("=" * 60)
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 创建示例配置文件
    create_sample_env_file()
    
    # 2. 展示模型选择示例
    show_model_selection_examples()
    
    # 3. 展示模型对比
    show_model_comparison()
    
    # 4. 展示配置指南
    show_configuration_guide()
    
    # 5. 展示使用场景
    show_usage_scenarios()
    
    # 6. 展示成本优化
    show_cost_optimization()
    
    print("\n🎉 演示完成!")
    print("\n📋 下一步操作:")
    print("  1. 编辑 .env 文件，添加真实的API密钥")
    print("  2. 运行 python test_models_simple.py 验证配置")
    print("  3. 选择合适的模型开始股票分析")
    print("  4. 根据需求和预算选择最佳模型组合")
    
    print("\n💡 提示:")
    print("  • 可以为不同的AI代理配置不同的模型")
    print("  • 建议先用便宜的模型测试，再用高质量模型精分析")
    print("  • Web界面支持动态切换模型，无需重启")

if __name__ == "__main__":
    main()
