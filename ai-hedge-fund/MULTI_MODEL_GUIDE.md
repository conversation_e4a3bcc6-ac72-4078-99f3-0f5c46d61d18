# 🤖 多模型AI系统使用指南

## 📋 概述

AI对冲基金系统支持**5个主要AI提供商的14个模型**，您可以根据不同的分析需求和成本考虑选择最合适的模型。

## 🎯 模型选择机制

### 🔄 **优先级顺序**
```
用户明确指定 > 代理特定配置 > 全局配置 > 系统默认(OpenAI GPT-4o)
```

### 📊 **支持的模型**

| 提供商 | 模型数量 | 代表模型 | 特点 |
|--------|----------|----------|------|
| **OpenAI** | 5个 | GPT-4o, o3, GPT-4.5 | 通用性强、JSON支持好 |
| **Anthropic** | 3个 | Claude Sonnet 4, Claude Opus 4 | 深度思考、安全性高 |
| **Google** | 2个 | Gemini 2.5 Flash, Gemini 2.5 Pro | 快速响应、成本效益 |
| **Groq** | 2个 | Llama 4 Scout, Llama 4 Maverick | 推理速度快、成本低 |
| **DeepSeek** | 2个 | DeepSeek R1, DeepSeek V3 | 推理能力强、中文支持 |

## ⚙️ 配置方法

### 1. **设置API密钥**

在项目根目录的 `.env` 文件中配置：

```bash
# OpenAI (默认)
OPENAI_API_KEY=sk-your-real-openai-key

# Anthropic
ANTHROPIC_API_KEY=sk-ant-your-real-anthropic-key

# Google
GOOGLE_API_KEY=AIza-your-real-google-key

# Groq
GROQ_API_KEY=gsk_your-real-groq-key

# DeepSeek
DEEPSEEK_API_KEY=sk-your-real-deepseek-key
```

### 2. **获取API密钥地址**

- **OpenAI**: https://platform.openai.com/ → API Keys
- **Anthropic**: https://anthropic.com/ → Console → API Keys  
- **Google**: https://ai.dev/ → Get API Key
- **Groq**: https://groq.com/ → Console → API Keys
- **DeepSeek**: https://deepseek.com/ → API Keys

## 🎮 使用方法

### 1. **命令行使用**

```bash
# 默认使用 OpenAI GPT-4o
python src/main.py --ticker AAPL

# 指定特定模型
python src/main.py --ticker AAPL --model claude-3-5-haiku-latest --provider Anthropic

# 分析中国A股
python src/main.py --ticker 000001,600036 --model deepseek-reasoner --provider DeepSeek

# 快速分析
python src/main.py --ticker AAPL,MSFT --model gemini-2.5-flash-preview-05-20 --provider Gemini
```

### 2. **Web界面使用**

```bash
# 启动Web界面
cd app && ./run.sh

# 在界面中动态选择模型，无需重启
```

## 🎭 使用场景建议

### 📈 **日常股票分析**
- **推荐**: OpenAI GPT-4o
- **原因**: 平衡的性能和成本，适合大多数分析需求
- **命令**: `python src/main.py --ticker AAPL`

### 🔍 **深度价值投资研究**
- **推荐**: Anthropic Claude Sonnet 4
- **原因**: 深度思考能力强，适合复杂的投资逻辑分析
- **命令**: `python src/main.py --ticker AAPL --model claude-sonnet-4-20250514 --provider Anthropic`

### ⚡ **快速市场扫描**
- **推荐**: Google Gemini 2.5 Flash
- **原因**: 响应速度快，成本低，适合大量股票筛选
- **命令**: `python src/main.py --ticker AAPL,MSFT,GOOGL --model gemini-2.5-flash-preview-05-20 --provider Gemini`

### 🏃 **大规模回测**
- **推荐**: Groq Llama 4
- **原因**: 推理速度极快，成本最低，适合批量处理
- **命令**: `python src/main.py --ticker AAPL --model meta-llama/llama-4-scout-17b-16e-instruct --provider Groq`

### 🇨🇳 **中国A股分析**
- **推荐**: DeepSeek R1
- **原因**: 中文理解能力强，适合中国市场特色分析
- **命令**: `python src/main.py --ticker 000001,600036 --model deepseek-reasoner --provider DeepSeek`

## 💰 成本优化策略

### 🔥 **高频使用场景**
- **首选**: Groq (最便宜) 
- **次选**: Gemini Flash (快速便宜)
- **避免**: Claude Opus (最贵)

### ⚡ **速度优先场景**
- **首选**: Groq Llama (推理速度最快)
- **次选**: Gemini Flash (响应快)

### 🎯 **质量优先场景**
- **首选**: Claude Sonnet (质量最高)
- **次选**: GPT-4o (平衡性好)

### 🔄 **混合策略**
```
初步筛选 → Groq/Gemini (快速便宜)
    ↓
深度分析 → Claude/GPT-4o (高质量)
    ↓
最终决策 → 人工审核 + AI辅助
```

## 🔧 高级配置

### 1. **为不同代理配置不同模型**

可以为不同的AI代理配置专门的模型：

- **Warren Buffett代理**: Claude (深度价值分析)
- **技术分析代理**: Gemini (快速计算)
- **投资组合管理器**: GPT-4o (综合决策)
- **中国市场代理**: DeepSeek (中文理解)

### 2. **动态模型切换**

系统支持运行时动态切换模型，无需重启应用。

### 3. **模型性能监控**

系统会自动记录各模型的：
- 响应时间
- 成功率
- 错误信息
- 成本统计

## 🧪 测试和验证

### 1. **配置验证**
```bash
# 测试所有模型配置
python test_models_simple.py

# 查看演示和配置指南
python demo_multi_models.py
```

### 2. **功能测试**
```bash
# 测试中美混合股票分析
python test_ai_analysis.py

# 测试AData集成
python test_adata_integration.py
```

## ⚠️ 注意事项

### 1. **API密钥安全**
- 不要将真实API密钥提交到版本控制
- 定期轮换API密钥
- 监控API使用量和成本

### 2. **模型限制**
- 不同模型有不同的上下文长度限制
- 某些模型不支持JSON模式
- 注意各提供商的使用条款

### 3. **成本控制**
- 监控API调用频率
- 设置合理的重试次数
- 选择适合场景的模型

## 🎉 总结

**多模型系统的优势**:
- 🎯 **灵活选择**: 根据需求选择最合适的模型
- 💰 **成本优化**: 平衡质量和成本
- ⚡ **性能提升**: 不同场景使用最优模型
- 🔄 **风险分散**: 避免单一模型依赖
- 🌍 **全球化**: 支持不同语言和市场

**系统默认使用OpenAI GPT-4o**，但您可以随时根据具体需求切换到其他模型，实现最佳的分析效果和成本控制！
