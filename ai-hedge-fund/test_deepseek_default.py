#!/usr/bin/env python3
"""
测试DeepSeek R1作为默认模型
验证系统是否正确使用DeepSeek R1进行分析
"""

import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 已加载 .env 文件")
except ImportError:
    # 手动加载.env文件
    env_path = '.env'
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
        print("✅ 手动加载 .env 文件成功")

def test_deepseek_api_key():
    """测试DeepSeek API密钥"""
    print("\n🔑 测试DeepSeek API密钥")
    print("=" * 50)
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if api_key and api_key.startswith("sk-"):
        print(f"✅ DeepSeek API密钥已配置: {api_key[:10]}...")
        return True
    else:
        print("❌ DeepSeek API密钥未配置或格式错误")
        return False

def test_default_model_config():
    """测试默认模型配置"""
    print("\n🎯 测试默认模型配置")
    print("=" * 50)
    
    try:
        from utils.llm import get_agent_model_config
        
        # 模拟空状态，应该使用默认配置
        class MockState:
            def get(self, key, default=None):
                if key == "metadata":
                    return {}
                return default
        
        state = MockState()
        model_name, model_provider = get_agent_model_config(state, None)
        
        print(f"📊 默认模型: {model_name}")
        print(f"📊 默认提供商: {model_provider}")
        
        # 验证是否为DeepSeek R1
        if model_name == "deepseek-reasoner" and model_provider == "DEEPSEEK":
            print("✅ 默认模型已正确设置为 DeepSeek R1")
            return True
        else:
            print("❌ 默认模型配置不正确")
            return False
            
    except Exception as e:
        print(f"❌ 默认模型测试失败: {str(e)}")
        return False

def test_deepseek_model_initialization():
    """测试DeepSeek模型初始化"""
    print("\n🤖 测试DeepSeek模型初始化")
    print("=" * 50)
    
    try:
        from llm.models import get_model, ModelProvider
        
        # 检查API密钥
        api_key = os.getenv("DEEPSEEK_API_KEY")
        if not api_key:
            print("❌ DeepSeek API密钥未设置")
            return False
        
        # 尝试初始化DeepSeek R1模型
        model = get_model("deepseek-reasoner", ModelProvider.DEEPSEEK)
        
        if model:
            print("✅ DeepSeek R1 模型初始化成功")
            return True
        else:
            print("❌ DeepSeek R1 模型初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ DeepSeek模型初始化错误: {str(e)}")
        return False

def test_simple_deepseek_call():
    """测试简单的DeepSeek调用"""
    print("\n🧪 测试DeepSeek R1调用")
    print("=" * 50)
    
    try:
        from langchain_deepseek import ChatDeepSeek
        
        api_key = os.getenv("DEEPSEEK_API_KEY")
        if not api_key:
            print("❌ DeepSeek API密钥未设置")
            return False
        
        # 初始化DeepSeek模型
        model = ChatDeepSeek(
            model="deepseek-reasoner",
            api_key=api_key,
            temperature=0.1
        )
        
        print("✅ DeepSeek R1 初始化成功")
        
        # 简单测试调用
        response = model.invoke("请用一句话简单介绍苹果公司(AAPL)的投资价值。")
        print(f"🤖 DeepSeek R1 回答: {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek调用失败: {str(e)}")
        return False

def test_reasoning_capability():
    """测试DeepSeek R1的推理能力"""
    print("\n🧠 测试DeepSeek R1推理能力")
    print("=" * 50)
    
    try:
        from langchain_deepseek import ChatDeepSeek
        
        api_key = os.getenv("DEEPSEEK_API_KEY")
        model = ChatDeepSeek(
            model="deepseek-reasoner",
            api_key=api_key,
            temperature=0.1
        )
        
        # 测试复杂推理
        prompt = """
        请分析苹果公司(AAPL)的投资价值，需要进行深度推理：
        1. 从财务角度分析其盈利能力
        2. 从竞争角度分析其市场地位
        3. 从创新角度分析其未来前景
        4. 综合给出投资建议和信心度
        
        请用JSON格式回答：
        {
            "recommendation": "buy/sell/hold",
            "confidence": 85,
            "reasoning": "详细推理过程"
        }
        """
        
        response = model.invoke(prompt)
        print(f"🧠 DeepSeek R1 推理分析:")
        print(response.content)
        
        return True
        
    except Exception as e:
        print(f"❌ 推理能力测试失败: {str(e)}")
        return False

def test_chinese_analysis():
    """测试中文分析能力"""
    print("\n🇨🇳 测试中文股票分析能力")
    print("=" * 50)
    
    try:
        from langchain_deepseek import ChatDeepSeek
        
        api_key = os.getenv("DEEPSEEK_API_KEY")
        model = ChatDeepSeek(
            model="deepseek-reasoner",
            api_key=api_key,
            temperature=0.1
        )
        
        # 测试中国股票分析
        prompt = """
        请深度分析平安银行(000001)的投资价值：
        
        作为中国领先的股份制商业银行，请从以下角度进行分析：
        1. 银行业务模式和盈利能力
        2. 在中国银行业的竞争地位
        3. 数字化转型和金融科技应用
        4. 风险控制和资产质量
        5. 未来发展前景和投资建议
        
        请用中文详细分析，并给出明确的投资建议。
        """
        
        response = model.invoke(prompt)
        print(f"🏦 中国A股深度分析:")
        print(response.content)
        
        return True
        
    except Exception as e:
        print(f"❌ 中文分析测试失败: {str(e)}")
        return False

def show_deepseek_advantages():
    """显示DeepSeek R1的优势"""
    print("\n🌟 DeepSeek R1 优势")
    print("=" * 60)
    
    advantages = [
        "🧠 **强大推理**: R1模型专为复杂推理任务设计",
        "🇨🇳 **中文优势**: 对中文理解和表达能力极强",
        "💰 **成本极低**: 比GPT-4o和Claude便宜很多",
        "🎯 **逻辑清晰**: 推理过程逻辑性强，分析深入",
        "📊 **金融专长**: 在金融分析领域表现优秀",
        "🔍 **深度分析**: 擅长多角度、多层次的分析",
        "⚡ **响应稳定**: 中国本土模型，网络稳定",
        "🌐 **双语能力**: 中英文切换自然，适合全球投资"
    ]
    
    for advantage in advantages:
        print(f"   {advantage}")

def show_usage_examples():
    """显示使用示例"""
    print("\n💡 DeepSeek R1 使用示例")
    print("=" * 60)
    
    examples = [
        {
            "description": "默认使用DeepSeek R1分析股票",
            "command": "python src/main.py --ticker AAPL",
            "note": "系统现在默认使用DeepSeek R1进行深度推理分析"
        },
        {
            "description": "深度分析中国A股",
            "command": "python src/main.py --ticker 000001,600036,000858",
            "note": "DeepSeek R1对中文理解极佳，特别适合A股分析"
        },
        {
            "description": "复杂投资组合分析",
            "command": "python src/main.py --ticker AAPL,MSFT,GOOGL,AMZN",
            "note": "利用强大推理能力进行复杂的投资组合分析"
        },
        {
            "description": "混合中美股票深度研究",
            "command": "python src/main.py --ticker AAPL,000001,MSFT,600036",
            "note": "同时分析中美股票，发挥双语优势"
        },
        {
            "description": "明确指定DeepSeek R1",
            "command": "python src/main.py --ticker AAPL --model deepseek-reasoner --provider DeepSeek",
            "note": "显式指定使用DeepSeek R1推理模型"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}️⃣ {example['description']}")
        print(f"   命令: {example['command']}")
        print(f"   说明: {example['note']}")

def main():
    """主测试函数"""
    print("🚀 DeepSeek R1 默认模型配置测试")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("🎯 目标: 将系统默认模型设置为 DeepSeek R1 推理模型")
    
    # 运行测试
    tests_passed = 0
    total_tests = 5
    
    try:
        # 1. 测试API密钥
        if test_deepseek_api_key():
            tests_passed += 1
        
        # 2. 测试默认模型配置
        if test_default_model_config():
            tests_passed += 1
        
        # 3. 测试模型初始化
        if test_deepseek_model_initialization():
            tests_passed += 1
        
        # 4. 测试基本调用
        if test_simple_deepseek_call():
            tests_passed += 1
        
        # 5. 测试推理能力
        if test_reasoning_capability():
            tests_passed += 1
        
        # 额外测试：中文分析能力
        test_chinese_analysis()
        
        # 显示优势
        show_deepseek_advantages()
        
        # 显示使用示例
        show_usage_examples()
        
        # 总结
        print(f"\n📊 测试总结")
        print("=" * 40)
        print(f"✅ 通过测试: {tests_passed}/{total_tests}")
        
        if tests_passed >= 4:  # 至少4个核心测试通过
            print("\n🎉 DeepSeek R1 配置成功!")
            print("\n✅ 系统现在将默认使用 DeepSeek R1")
            print("✅ 强大的推理能力，适合复杂分析")
            print("✅ 中文理解极佳，特别适合A股分析")
            print("✅ 成本极低，适合大量使用")
            print("✅ 逻辑清晰，分析深度强")
            
            print("\n🔧 下一步:")
            print("  1. 运行: python src/main.py --ticker AAPL")
            print("  2. 或启动Web界面: cd app && ./run.sh")
            print("  3. 享受DeepSeek R1的强大推理分析!")
            
            print("\n💡 特别推荐:")
            print("  • 使用DeepSeek R1分析中国A股")
            print("  • 进行复杂的投资组合分析")
            print("  • 需要深度推理的投资决策")
            print("  • 成本敏感的大规模分析")
        else:
            print(f"\n⚠️  还有 {total_tests - tests_passed} 个测试未通过")
            print("请检查DeepSeek API密钥配置")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
