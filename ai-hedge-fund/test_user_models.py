#!/usr/bin/env python3
"""
用户指定模型配置测试脚本
测试用户配置的4个模型：GPT-4o, <PERSON> 4, DeepSeek R1, Gemini 2.5 Pro
"""

import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 已加载 .env 文件")
except ImportError:
    print("⚠️  python-dotenv 未安装，尝试手动加载 .env 文件")
    # 手动加载.env文件
    env_path = '.env'
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
        print("✅ 手动加载 .env 文件成功")
    else:
        print("❌ .env 文件不存在")

def test_api_keys():
    """测试用户配置的API密钥"""
    print("🔑 测试用户配置的API密钥")
    print("=" * 60)
    
    # 用户指定的模型配置
    user_models = {
        "OpenAI GPT-4o": {
            "env_var": "OPENAI_API_KEY",
            "expected_prefix": "sk-",
            "model_name": "gpt-4o",
            "provider": "OPENAI"
        },
        "Claude Sonnet 4": {
            "env_var": "ANTHROPIC_API_KEY", 
            "expected_prefix": "sk-ant-",
            "model_name": "claude-sonnet-4-20250514",
            "provider": "ANTHROPIC"
        },
        "DeepSeek R1": {
            "env_var": "DEEPSEEK_API_KEY",
            "expected_prefix": "sk-",
            "model_name": "deepseek-reasoner", 
            "provider": "DEEPSEEK"
        },
        "Gemini 2.5 Pro": {
            "env_var": "GOOGLE_API_KEY",
            "expected_prefix": "AIza",
            "model_name": "gemini-2.5-pro-preview-06-05",
            "provider": "GEMINI"
        }
    }
    
    configured_models = []
    
    for model_name, config in user_models.items():
        api_key = os.getenv(config["env_var"])
        
        if api_key and api_key.startswith(config["expected_prefix"]):
            print(f"✅ {model_name}: API密钥已正确配置")
            print(f"   模型: {config['model_name']}")
            print(f"   提供商: {config['provider']}")
            print(f"   密钥前缀: {api_key[:10]}...")
            configured_models.append(config)
        else:
            print(f"❌ {model_name}: API密钥未配置或格式错误")
            if api_key:
                print(f"   当前值: {api_key[:10]}...")
            else:
                print(f"   环境变量 {config['env_var']} 未设置")
        print()
    
    print(f"📊 成功配置: {len(configured_models)}/4 个模型")
    return configured_models

def test_model_initialization():
    """测试模型初始化"""
    print("\n🤖 测试模型初始化")
    print("=" * 60)
    
    try:
        from llm.models import get_model, ModelProvider
        print("✅ 模型管理模块导入成功")
    except Exception as e:
        print(f"❌ 模型管理模块导入失败: {str(e)}")
        return []
    
    # 用户指定的模型测试
    user_model_tests = [
        ("gpt-4o", ModelProvider.OPENAI, "OpenAI GPT-4o"),
        ("claude-sonnet-4-20250514", ModelProvider.ANTHROPIC, "Claude Sonnet 4"),
        ("deepseek-reasoner", ModelProvider.DEEPSEEK, "DeepSeek R1"),
        ("gemini-2.5-pro-preview-06-05", ModelProvider.GEMINI, "Gemini 2.5 Pro")
    ]
    
    successful_models = []
    
    for model_name, provider, description in user_model_tests:
        try:
            print(f"🔄 测试 {description}")
            
            # 检查对应的API密钥
            api_key_map = {
                ModelProvider.OPENAI: "OPENAI_API_KEY",
                ModelProvider.ANTHROPIC: "ANTHROPIC_API_KEY",
                ModelProvider.GEMINI: "GOOGLE_API_KEY", 
                ModelProvider.DEEPSEEK: "DEEPSEEK_API_KEY"
            }
            
            api_key = os.getenv(api_key_map[provider])
            if not api_key or "your-" in api_key:
                print(f"⚠️  跳过 {description} - API密钥未配置")
                continue
            
            # 尝试初始化模型
            model = get_model(model_name, provider)
            if model:
                print(f"✅ {description} 初始化成功")
                successful_models.append((model_name, provider, description))
            else:
                print(f"❌ {description} 初始化失败")
                
        except Exception as e:
            print(f"❌ {description} 初始化错误: {str(e)}")
    
    print(f"\n📊 成功初始化: {len(successful_models)}/4 个模型")
    return successful_models

def test_default_model():
    """测试默认模型配置"""
    print("\n🎯 测试默认模型配置")
    print("=" * 60)
    
    try:
        from utils.llm import get_agent_model_config
        
        # 模拟空状态，应该使用默认配置
        class MockState:
            def get(self, key, default=None):
                if key == "metadata":
                    return {}
                return default
        
        state = MockState()
        model_name, model_provider = get_agent_model_config(state, None)
        
        print(f"📊 默认模型: {model_name}")
        print(f"📊 默认提供商: {model_provider}")
        
        # 验证是否为用户指定的GPT-4o
        if model_name == "gpt-4o" and model_provider == "OPENAI":
            print("✅ 默认模型配置正确 (GPT-4o)")
        else:
            print("⚠️  默认模型配置与用户要求不符")
        
        # 检查API密钥
        api_key = os.getenv("OPENAI_API_KEY")
        if api_key and api_key.startswith("sk-"):
            print("✅ 默认模型API密钥已配置")
        else:
            print("❌ 默认模型API密钥未配置")
            
    except Exception as e:
        print(f"❌ 默认模型测试失败: {str(e)}")

def show_usage_examples():
    """显示使用示例"""
    print("\n💡 使用示例")
    print("=" * 60)
    
    examples = [
        {
            "description": "使用默认GPT-4o分析股票",
            "command": "python src/main.py --ticker AAPL",
            "note": "系统会自动使用您配置的GPT-4o"
        },
        {
            "description": "使用Claude Sonnet 4进行深度分析",
            "command": "python src/main.py --ticker AAPL --model claude-sonnet-4-20250514 --provider Anthropic",
            "note": "适合复杂的价值投资分析"
        },
        {
            "description": "使用DeepSeek R1分析中国A股",
            "command": "python src/main.py --ticker 000001,600036 --model deepseek-reasoner --provider DeepSeek",
            "note": "中文理解能力强，适合A股分析"
        },
        {
            "description": "使用Gemini 2.5 Pro进行快速分析",
            "command": "python src/main.py --ticker AAPL,MSFT --model gemini-2.5-pro-preview-06-05 --provider Gemini",
            "note": "高性能版本，适合复杂任务"
        },
        {
            "description": "混合中美股票分析",
            "command": "python src/main.py --ticker AAPL,000001,MSFT,600036",
            "note": "使用默认GPT-4o分析混合投资组合"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}️⃣ {example['description']}")
        print(f"   命令: {example['command']}")
        print(f"   说明: {example['note']}")

def test_web_interface():
    """测试Web界面配置"""
    print("\n🌐 Web界面使用说明")
    print("=" * 60)
    
    print("启动Web界面:")
    print("   cd app && ./run.sh")
    print()
    print("在Web界面中，您可以:")
    print("   • 动态选择您配置的4个模型")
    print("   • 实时切换模型，无需重启")
    print("   • 可视化查看分析结果")
    print("   • 对比不同模型的分析结果")

def main():
    """主测试函数"""
    print("🚀 用户指定模型配置测试")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("📋 用户指定的模型配置:")
    print("   • OpenAI: GPT-4o")
    print("   • Anthropic: Claude Sonnet 4") 
    print("   • DeepSeek: DeepSeek R1")
    print("   • Google: Gemini 2.5 Pro")
    print()
    
    try:
        # 1. 测试API密钥配置
        configured_models = test_api_keys()
        
        # 2. 测试模型初始化
        successful_models = test_model_initialization()
        
        # 3. 测试默认模型
        test_default_model()
        
        # 4. 显示使用示例
        show_usage_examples()
        
        # 5. Web界面说明
        test_web_interface()
        
        # 总结
        print("\n📊 配置总结")
        print("=" * 40)
        print(f"✅ API密钥配置: {len(configured_models)}/4")
        print(f"✅ 模型初始化: {len(successful_models)}/4")
        print(f"✅ 默认模型: GPT-4o (OpenAI)")
        
        if len(configured_models) == 4:
            print("\n🎉 所有模型配置成功!")
            print("您现在可以:")
            print("  • 使用默认GPT-4o进行日常分析")
            print("  • 切换到Claude Sonnet 4进行深度研究")
            print("  • 使用DeepSeek R1分析中国A股")
            print("  • 使用Gemini 2.5 Pro进行高性能分析")
        else:
            print(f"\n⚠️  还有 {4-len(configured_models)} 个模型需要配置")
            print("请检查.env文件中的API密钥设置")
        
        print("\n🔧 下一步:")
        print("  1. 运行: python src/main.py --ticker AAPL (测试默认GPT-4o)")
        print("  2. 或启动Web界面: cd app && ./run.sh")
        print("  3. 在界面中选择不同模型进行对比分析")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
