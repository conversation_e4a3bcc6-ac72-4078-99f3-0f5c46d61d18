#!/usr/bin/env python3
"""
简化的多模型测试脚本
测试4个主要AI模型提供商的配置和使用
"""

import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_environment_setup():
    """测试环境配置"""
    print("🔧 环境配置检查")
    print("=" * 50)
    
    # 检查.env文件
    if os.path.exists('.env'):
        print("✅ .env文件存在")
    else:
        print("❌ .env文件不存在")
        return False
    
    # 检查API密钥配置
    api_keys = {
        "OpenAI": "OPENAI_API_KEY",
        "Anthropic": "ANTHROPIC_API_KEY", 
        "Google": "GOOGLE_API_KEY",
        "Groq": "GROQ_API_KEY",
        "DeepSeek": "DEEPSEEK_API_KEY"
    }
    
    configured_keys = []
    
    for provider, env_var in api_keys.items():
        api_key = os.getenv(env_var)
        if api_key and not api_key.startswith("your-") and not api_key.startswith("sk-your-") and not api_key.startswith("AIza-your-"):
            print(f"✅ {provider}: API密钥已配置")
            configured_keys.append(provider)
        else:
            print(f"⚠️  {provider}: API密钥未配置或为示例值")
    
    print(f"\n📊 已配置的API密钥: {len(configured_keys)}/{len(api_keys)}")
    return configured_keys

def test_model_imports():
    """测试模型导入"""
    print("\n📦 模型库导入测试")
    print("=" * 50)
    
    import_results = {}
    
    # 测试各个模型库的导入
    try:
        from langchain_openai import ChatOpenAI
        print("✅ OpenAI: langchain_openai 导入成功")
        import_results["OpenAI"] = True
    except Exception as e:
        print(f"❌ OpenAI: 导入失败 - {str(e)}")
        import_results["OpenAI"] = False
    
    try:
        from langchain_anthropic import ChatAnthropic
        print("✅ Anthropic: langchain_anthropic 导入成功")
        import_results["Anthropic"] = True
    except Exception as e:
        print(f"❌ Anthropic: 导入失败 - {str(e)}")
        import_results["Anthropic"] = False
    
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        print("✅ Google: langchain_google_genai 导入成功")
        import_results["Google"] = True
    except Exception as e:
        print(f"❌ Google: 导入失败 - {str(e)}")
        import_results["Google"] = False
    
    try:
        from langchain_groq import ChatGroq
        print("✅ Groq: langchain_groq 导入成功")
        import_results["Groq"] = True
    except Exception as e:
        print(f"❌ Groq: 导入失败 - {str(e)}")
        import_results["Groq"] = False
    
    try:
        from langchain_deepseek import ChatDeepSeek
        print("✅ DeepSeek: langchain_deepseek 导入成功")
        import_results["DeepSeek"] = True
    except Exception as e:
        print(f"❌ DeepSeek: 导入失败 - {str(e)}")
        import_results["DeepSeek"] = False
    
    successful_imports = sum(import_results.values())
    print(f"\n📊 成功导入: {successful_imports}/{len(import_results)} 个模型库")
    
    return import_results

def test_model_initialization():
    """测试模型初始化"""
    print("\n🤖 模型初始化测试")
    print("=" * 50)
    
    # 导入必要的模块
    try:
        from llm.models import get_model, ModelProvider
        print("✅ 成功导入模型管理模块")
    except Exception as e:
        print(f"❌ 模型管理模块导入失败: {str(e)}")
        return {}
    
    # 测试模型配置
    test_configs = [
        ("gpt-4o", ModelProvider.OPENAI, "OpenAI GPT-4o"),
        ("claude-3-5-haiku-latest", ModelProvider.ANTHROPIC, "Anthropic Claude"),
        ("gemini-2.5-flash-preview-05-20", ModelProvider.GEMINI, "Google Gemini"),
        ("meta-llama/llama-4-scout-17b-16e-instruct", ModelProvider.GROQ, "Groq Llama"),
        ("deepseek-chat", ModelProvider.DEEPSEEK, "DeepSeek Chat")
    ]
    
    initialization_results = {}
    
    for model_name, provider, description in test_configs:
        try:
            print(f"🔄 测试 {description}")
            
            # 检查对应的API密钥
            api_key_map = {
                ModelProvider.OPENAI: "OPENAI_API_KEY",
                ModelProvider.ANTHROPIC: "ANTHROPIC_API_KEY",
                ModelProvider.GEMINI: "GOOGLE_API_KEY",
                ModelProvider.GROQ: "GROQ_API_KEY",
                ModelProvider.DEEPSEEK: "DEEPSEEK_API_KEY"
            }
            
            api_key = os.getenv(api_key_map[provider])
            if not api_key or "your-" in api_key:
                print(f"⚠️  跳过 {description} - API密钥未配置")
                initialization_results[provider.value] = "未配置"
                continue
            
            # 尝试初始化模型
            model = get_model(model_name, provider)
            if model:
                print(f"✅ {description} 初始化成功")
                initialization_results[provider.value] = "成功"
            else:
                print(f"❌ {description} 初始化失败")
                initialization_results[provider.value] = "失败"
                
        except Exception as e:
            print(f"❌ {description} 初始化错误: {str(e)}")
            initialization_results[provider.value] = f"错误: {str(e)[:50]}..."
    
    return initialization_results

def test_default_behavior():
    """测试默认模型行为"""
    print("\n🎯 默认模型行为测试")
    print("=" * 50)
    
    try:
        from utils.llm import call_llm
        from pydantic import BaseModel
        from typing import Literal
        
        class TestResponse(BaseModel):
            message: str
            status: Literal["success", "error"]
        
        print("1️⃣ 测试默认模型选择逻辑")
        
        # 模拟状态对象
        class MockState:
            def get(self, key, default=None):
                if key == "metadata":
                    return {}  # 空的metadata，应该使用默认值
                return default
        
        state = MockState()
        
        # 检查默认配置
        from utils.llm import get_agent_model_config
        model_name, model_provider = get_agent_model_config(state, None)
        
        print(f"📊 默认模型: {model_name}")
        print(f"📊 默认提供商: {model_provider}")
        
        # 检查默认模型的API密钥
        if model_provider == "OPENAI":
            api_key = os.getenv("OPENAI_API_KEY")
            if api_key and not "your-" in api_key:
                print("✅ 默认模型API密钥已配置")
            else:
                print("⚠️  默认模型API密钥未配置")
        
    except Exception as e:
        print(f"❌ 默认行为测试失败: {str(e)}")

def test_model_switching():
    """测试模型切换功能"""
    print("\n🔄 模型切换测试")
    print("=" * 50)
    
    # 模拟不同的命令行参数
    test_scenarios = [
        ("无参数", None, None, "应使用默认 OpenAI GPT-4o"),
        ("指定OpenAI", "gpt-4o", "OPENAI", "应使用 OpenAI GPT-4o"),
        ("指定Anthropic", "claude-3-5-haiku-latest", "ANTHROPIC", "应使用 Anthropic Claude"),
        ("指定Google", "gemini-2.5-flash-preview-05-20", "GEMINI", "应使用 Google Gemini"),
        ("指定Groq", "meta-llama/llama-4-scout-17b-16e-instruct", "GROQ", "应使用 Groq Llama"),
    ]
    
    for scenario_name, model_name, provider, expected in test_scenarios:
        print(f"\n🎯 场景: {scenario_name}")
        print(f"   参数: --model {model_name or '(默认)'} --provider {provider or '(默认)'}")
        print(f"   预期: {expected}")
        
        # 模拟状态配置
        if model_name and provider:
            metadata = {
                "model_name": model_name,
                "model_provider": provider
            }
        else:
            metadata = {}
        
        class MockState:
            def get(self, key, default=None):
                if key == "metadata":
                    return metadata
                return default
        
        try:
            from utils.llm import get_agent_model_config
            actual_model, actual_provider = get_agent_model_config(MockState(), None)
            print(f"   实际: {actual_model} ({actual_provider})")
            
            # 检查API密钥可用性
            api_key_map = {
                "OPENAI": "OPENAI_API_KEY",
                "ANTHROPIC": "ANTHROPIC_API_KEY",
                "GEMINI": "GOOGLE_API_KEY",
                "GROQ": "GROQ_API_KEY",
                "DEEPSEEK": "DEEPSEEK_API_KEY"
            }
            
            api_key = os.getenv(api_key_map.get(actual_provider, "OPENAI_API_KEY"))
            if api_key and not "your-" in api_key:
                print("   ✅ API密钥可用")
            else:
                print("   ⚠️  API密钥不可用")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {str(e)}")

def show_available_models():
    """显示可用的模型列表"""
    print("\n📋 可用模型列表")
    print("=" * 50)
    
    try:
        from llm.models import get_models_list
        models = get_models_list()
        
        print(f"系统中配置了 {len(models)} 个模型:")
        
        by_provider = {}
        for model in models:
            provider = model['provider']
            if provider not in by_provider:
                by_provider[provider] = []
            by_provider[provider].append(model)
        
        for provider, provider_models in by_provider.items():
            print(f"\n🏢 {provider} ({len(provider_models)} 个模型):")
            for model in provider_models:
                print(f"   • {model['display_name']} ({model['model_name']})")
        
    except Exception as e:
        print(f"❌ 获取模型列表失败: {str(e)}")

def main():
    """主测试函数"""
    print("🚀 AI模型系统测试 - 4个模型提供商")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 环境配置检查
        configured_keys = test_environment_setup()
        
        # 2. 模型库导入测试
        import_results = test_model_imports()
        
        # 3. 显示可用模型
        show_available_models()
        
        # 4. 模型初始化测试
        init_results = test_model_initialization()
        
        # 5. 默认行为测试
        test_default_behavior()
        
        # 6. 模型切换测试
        test_model_switching()
        
        # 总结
        print("\n📊 测试总结")
        print("=" * 30)
        print(f"✅ 已配置API密钥: {len(configured_keys)} 个")
        print(f"✅ 成功导入模型库: {sum(import_results.values())} 个")
        print(f"✅ 可初始化模型: {sum(1 for v in init_results.values() if v == '成功')} 个")
        
        print("\n💡 使用说明:")
        print("  1. 在.env文件中设置真实的API密钥")
        print("  2. 使用命令行参数指定模型:")
        print("     python src/main.py --ticker AAPL --model gpt-4o --provider OPENAI")
        print("  3. 或在Web界面中动态选择模型")
        print("  4. 系统默认使用 OpenAI GPT-4o")
        
        if not configured_keys:
            print("\n⚠️  警告: 没有配置任何API密钥，请在.env文件中添加真实的API密钥")
        
        print("\n🎉 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
