#!/usr/bin/env python3
"""
投资决策辅助框架
将AI分析结果转化为实际投资决策的工具
"""

import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

class InvestmentDecisionFramework:
    """投资决策框架"""
    
    def __init__(self):
        self.risk_levels = {
            "conservative": {"max_position": 0.05, "stop_loss": 0.08, "take_profit": 0.15},
            "moderate": {"max_position": 0.10, "stop_loss": 0.12, "take_profit": 0.25},
            "aggressive": {"max_position": 0.20, "stop_loss": 0.15, "take_profit": 0.40}
        }
    
    def analyze_ai_recommendation(self, stock_code: str, ai_analysis: Dict):
        """分析AI推荐结果"""
        print(f"\n📊 {stock_code} AI分析结果评估")
        print("=" * 60)
        
        # 提取关键信息
        recommendation = ai_analysis.get("recommendation", "hold")
        target_price = ai_analysis.get("target_price", "未明确")
        confidence = ai_analysis.get("confidence", 50)
        key_risks = ai_analysis.get("risks", [])
        
        print(f"🎯 AI推荐: {recommendation}")
        print(f"💰 目标价位: {target_price}")
        print(f"📈 信心度: {confidence}%")
        print(f"⚠️  主要风险: {', '.join(key_risks)}")
        
        return {
            "recommendation": recommendation,
            "target_price": target_price,
            "confidence": confidence,
            "risks": key_risks
        }
    
    def create_verification_checklist(self, stock_code: str):
        """创建验证清单"""
        print(f"\n✅ {stock_code} 投资前验证清单")
        print("=" * 60)
        
        checklist = [
            "📊 查看最新财务数据（季报/年报）",
            "📰 关注最新公司公告和新闻",
            "🏭 了解行业最新动态和政策变化",
            "📈 分析技术面和资金流向",
            "🔍 阅读券商最新研报",
            "💼 评估管理层能力和公司治理",
            "🌍 考虑宏观经济环境影响",
            "⚖️  评估估值合理性",
            "🎯 明确投资逻辑和预期收益",
            "⚠️  识别主要风险点"
        ]
        
        for i, item in enumerate(checklist, 1):
            print(f"{i:2d}. {item}")
        
        print("\n💡 建议：逐项核实后再做投资决策")
        return checklist
    
    def calculate_position_size(self, total_capital: float, risk_level: str, confidence: int):
        """计算建议仓位"""
        print(f"\n💰 仓位管理建议")
        print("=" * 60)
        
        if risk_level not in self.risk_levels:
            risk_level = "moderate"
        
        base_position = self.risk_levels[risk_level]["max_position"]
        
        # 根据信心度调整仓位
        confidence_factor = confidence / 100
        adjusted_position = base_position * confidence_factor
        
        # 计算具体金额
        position_amount = total_capital * adjusted_position
        
        print(f"🎯 风险偏好: {risk_level}")
        print(f"📊 基础仓位: {base_position:.1%}")
        print(f"🧠 信心度调整: {confidence}% → {confidence_factor:.2f}")
        print(f"💵 建议仓位: {adjusted_position:.1%}")
        print(f"💰 建议金额: ¥{position_amount:,.0f}")
        
        return {
            "position_ratio": adjusted_position,
            "position_amount": position_amount,
            "risk_level": risk_level
        }
    
    def set_risk_management(self, entry_price: float, risk_level: str):
        """设置风险管理"""
        print(f"\n⚠️  风险管理设置")
        print("=" * 60)
        
        if risk_level not in self.risk_levels:
            risk_level = "moderate"
        
        stop_loss_ratio = self.risk_levels[risk_level]["stop_loss"]
        take_profit_ratio = self.risk_levels[risk_level]["take_profit"]
        
        stop_loss_price = entry_price * (1 - stop_loss_ratio)
        take_profit_price = entry_price * (1 + take_profit_ratio)
        
        print(f"🎯 入场价格: ¥{entry_price:.2f}")
        print(f"🛑 止损价格: ¥{stop_loss_price:.2f} (-{stop_loss_ratio:.1%})")
        print(f"🎉 止盈价格: ¥{take_profit_price:.2f} (+{take_profit_ratio:.1%})")
        print(f"📊 风险收益比: 1:{take_profit_ratio/stop_loss_ratio:.1f}")
        
        return {
            "entry_price": entry_price,
            "stop_loss": stop_loss_price,
            "take_profit": take_profit_price,
            "risk_reward_ratio": take_profit_ratio/stop_loss_ratio
        }
    
    def create_monitoring_plan(self, stock_code: str):
        """创建监控计划"""
        print(f"\n👀 {stock_code} 持续监控计划")
        print("=" * 60)
        
        monitoring_items = [
            "📊 每周关注财务指标变化",
            "📰 每日关注公司公告和新闻",
            "🏭 每月评估行业发展趋势",
            "📈 每日监控技术面和成交量",
            "💰 每季度评估基本面变化",
            "🎯 每月检查投资逻辑是否成立",
            "⚠️  随时关注风险因素变化",
            "🔄 定期调整止损止盈位置"
        ]
        
        for i, item in enumerate(monitoring_items, 1):
            print(f"{i}. {item}")
        
        return monitoring_items

def demonstrate_framework():
    """演示投资决策框架"""
    print("🎯 AI辅助投资决策框架演示")
    print("=" * 80)
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建框架实例
    framework = InvestmentDecisionFramework()
    
    # 模拟AI分析结果
    ai_results = {
        "000062": {
            "recommendation": "持有",
            "target_price": "9.5-10.5元",
            "confidence": 70,
            "risks": ["消费电子需求疲软", "应收账款风险", "行业竞争加剧"]
        },
        "300120": {
            "recommendation": "观望",
            "target_price": "6.5-8.5元", 
            "confidence": 45,
            "risks": ["技术量产不确定", "财务压力", "商誉减值风险"]
        },
        "002428": {
            "recommendation": "谨慎持有",
            "target_price": "16-18元",
            "confidence": 65,
            "risks": ["锗价波动", "技术替代", "产能消化"]
        }
    }
    
    # 演示每只股票的决策流程
    for stock_code, analysis in ai_results.items():
        print("\n" + "="*80)
        
        # 1. 分析AI推荐
        ai_summary = framework.analyze_ai_recommendation(stock_code, analysis)
        
        # 2. 创建验证清单
        framework.create_verification_checklist(stock_code)
        
        # 3. 仓位管理（假设总资金100万）
        position_info = framework.calculate_position_size(
            total_capital=1000000,
            risk_level="moderate",
            confidence=analysis["confidence"]
        )
        
        # 4. 风险管理（假设当前价格）
        current_prices = {"000062": 8.5, "300120": 7.2, "002428": 15.8}
        risk_info = framework.set_risk_management(
            entry_price=current_prices[stock_code],
            risk_level="moderate"
        )
        
        # 5. 监控计划
        framework.create_monitoring_plan(stock_code)
        
        print(f"\n📋 {stock_code} 决策总结:")
        print(f"   AI推荐: {ai_summary['recommendation']} (信心度: {ai_summary['confidence']}%)")
        print(f"   建议仓位: {position_info['position_ratio']:.1%}")
        print(f"   风险管理: 止损{risk_info['stop_loss']:.2f} / 止盈{risk_info['take_profit']:.2f}")
        print(f"   下一步: 完成验证清单后再决策")

def show_investment_principles():
    """显示投资原则"""
    print("\n💡 AI辅助投资的核心原则")
    print("=" * 80)
    
    principles = [
        "🤖 AI分析仅作参考，不能替代人工判断",
        "📊 必须验证AI提到的所有关键数据",
        "🕐 关注信息时效性，获取最新数据",
        "⚖️  结合个人风险承受能力制定策略",
        "💰 严格执行仓位管理和风险控制",
        "👀 建立持续监控和动态调整机制",
        "📚 不断学习，提升投资决策能力",
        "🎯 保持理性，避免情绪化决策"
    ]
    
    for principle in principles:
        print(f"   {principle}")
    
    print("\n⚠️  重要提醒:")
    print("   • 投资有风险，入市需谨慎")
    print("   • 过往业绩不代表未来表现") 
    print("   • 请根据自身情况做出投资决策")
    print("   • 建议咨询专业投资顾问")

if __name__ == "__main__":
    # 演示投资决策框架
    demonstrate_framework()
    
    # 显示投资原则
    show_investment_principles()
    
    print("\n🎉 投资决策框架演示完成!")
    print("\n📝 使用建议:")
    print("   1. 将此框架作为投资决策的辅助工具")
    print("   2. 严格按照验证清单执行尽职调查")
    print("   3. 根据个人情况调整风险参数")
    print("   4. 建立完善的监控和调整机制")
