#!/usr/bin/env python3
"""
DeepSeek R1最终配置验证脚本
验证所有问题是否已完全解决
"""

import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 已加载 .env 文件")
except ImportError:
    # 手动加载.env文件
    env_path = '.env'
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
        print("✅ 手动加载 .env 文件成功")

def test_all_configurations():
    """测试所有配置"""
    print("\n🔧 完整配置验证")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 6
    
    # 1. 测试API密钥
    print("1️⃣ 测试DeepSeek API密钥")
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if api_key and api_key.startswith("sk-"):
        print(f"   ✅ API密钥已配置: {api_key[:10]}...")
        tests_passed += 1
    else:
        print("   ❌ API密钥配置错误")
    
    # 2. 测试默认模型配置
    print("\n2️⃣ 测试默认模型配置")
    try:
        from utils.llm import get_agent_model_config
        
        class MockState:
            def get(self, key, default=None):
                if key == "metadata":
                    return {}
                return default
        
        state = MockState()
        model_name, model_provider = get_agent_model_config(state, None)
        
        if model_name == "deepseek-reasoner" and model_provider == "DEEPSEEK":
            print(f"   ✅ 默认模型: {model_name} ({model_provider})")
            tests_passed += 1
        else:
            print(f"   ❌ 默认模型错误: {model_name} ({model_provider})")
    except Exception as e:
        print(f"   ❌ 默认模型测试失败: {str(e)}")
    
    # 3. 测试模型初始化
    print("\n3️⃣ 测试模型初始化")
    try:
        from llm.models import get_model, ModelProvider
        model = get_model("deepseek-reasoner", ModelProvider.DEEPSEEK)
        if model:
            print("   ✅ DeepSeek R1 模型初始化成功")
            tests_passed += 1
        else:
            print("   ❌ 模型初始化失败")
    except Exception as e:
        print(f"   ❌ 模型初始化错误: {str(e)}")
    
    # 4. 测试实际调用
    print("\n4️⃣ 测试模型调用")
    try:
        from llm.models import get_model, ModelProvider
        model = get_model("deepseek-reasoner", ModelProvider.DEEPSEEK)
        response = model.invoke("简单介绍苹果公司投资价值，一句话。")
        if response and response.content:
            print(f"   ✅ 调用成功: {response.content[:50]}...")
            tests_passed += 1
        else:
            print("   ❌ 调用失败")
    except Exception as e:
        print(f"   ❌ 调用错误: {str(e)}")
    
    # 5. 测试系统集成
    print("\n5️⃣ 测试系统集成")
    try:
        from utils.llm import call_llm
        from pydantic import BaseModel
        
        class TestResponse(BaseModel):
            recommendation: str
            confidence: int
        
        class MockState:
            def get(self, key, default=None):
                if key == "metadata":
                    return {}
                return default
        
        state = MockState()
        result = call_llm(
            state=state,
            prompt="分析苹果公司投资价值",
            response_model=TestResponse,
            agent_name="test_agent"
        )
        
        if result and hasattr(result, 'recommendation'):
            print(f"   ✅ 系统集成成功: {result.recommendation}")
            tests_passed += 1
        else:
            print("   ❌ 系统集成失败")
    except Exception as e:
        print(f"   ❌ 系统集成错误: {str(e)}")
    
    # 6. 测试Web界面兼容性
    print("\n6️⃣ 测试Web界面兼容性")
    try:
        # 检查模型列表是否包含DeepSeek
        from llm.models import get_models_list
        models = get_models_list()
        deepseek_models = [m for m in models if m['provider'] == 'DeepSeek']
        
        if deepseek_models:
            print(f"   ✅ Web界面兼容: 找到 {len(deepseek_models)} 个DeepSeek模型")
            tests_passed += 1
        else:
            print("   ❌ Web界面不兼容: 未找到DeepSeek模型")
    except Exception as e:
        print(f"   ❌ Web界面测试错误: {str(e)}")
    
    return tests_passed, total_tests

def test_comprehensive_analysis():
    """测试综合分析能力"""
    print("\n🧠 综合分析能力测试")
    print("=" * 60)
    
    try:
        from llm.models import get_model, ModelProvider
        model = get_model("deepseek-reasoner", ModelProvider.DEEPSEEK)
        
        # 测试复杂分析
        prompt = """
        请分析苹果公司(AAPL)的投资价值，要求：
        1. 从财务、竞争、创新三个角度分析
        2. 给出明确的投资建议
        3. 用JSON格式回答：
        {
            "recommendation": "buy/sell/hold",
            "confidence": 85,
            "key_points": ["要点1", "要点2", "要点3"]
        }
        """
        
        response = model.invoke(prompt)
        print("📊 DeepSeek R1 综合分析结果:")
        print(response.content[:500] + "..." if len(response.content) > 500 else response.content)
        
        return True
        
    except Exception as e:
        print(f"❌ 综合分析测试失败: {str(e)}")
        return False

def show_usage_guide():
    """显示使用指南"""
    print("\n💡 DeepSeek R1 使用指南")
    print("=" * 60)
    
    print("🚀 立即开始使用:")
    print("   1. 默认分析: python src/main.py --ticker AAPL")
    print("   2. A股分析: python src/main.py --ticker 000001,600036")
    print("   3. Web界面: cd app && ./run.sh")
    
    print("\n🌟 DeepSeek R1 优势:")
    print("   • 🧠 强大推理能力，专为复杂分析设计")
    print("   • 🇨🇳 中文理解极佳，特别适合A股分析")
    print("   • 💰 成本极低，比GPT-4o和Claude便宜很多")
    print("   • 🎯 逻辑清晰，分析深度强")
    print("   • 📊 金融专长，在投资分析领域表现优秀")
    
    print("\n🎭 AI代理配置:")
    print("   • 所有17个AI代理现在都使用DeepSeek R1")
    print("   • Warren Buffett代理 → DeepSeek R1深度价值分析")
    print("   • 技术分析代理 → DeepSeek R1逻辑推理分析")
    print("   • 风险管理器 → DeepSeek R1风险推理分析")

def main():
    """主函数"""
    print("🎉 DeepSeek R1 最终配置验证")
    print("=" * 80)
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("🎯 目标: 验证DeepSeek R1配置是否完全成功")
    
    try:
        # 完整配置验证
        tests_passed, total_tests = test_all_configurations()
        
        # 综合分析测试
        analysis_success = test_comprehensive_analysis()
        
        # 总结
        print(f"\n📊 最终验证结果")
        print("=" * 40)
        print(f"✅ 配置测试: {tests_passed}/{total_tests}")
        print(f"✅ 分析测试: {'通过' if analysis_success else '失败'}")
        
        if tests_passed >= 5 and analysis_success:
            print("\n🎉 DeepSeek R1 配置完全成功!")
            print("\n✅ 所有配置问题已解决")
            print("✅ API密钥正常工作")
            print("✅ 默认模型已设置为DeepSeek R1")
            print("✅ 模型初始化和调用正常")
            print("✅ 系统集成完全兼容")
            print("✅ Web界面支持DeepSeek模型")
            print("✅ 综合分析能力优秀")
            
            # 显示使用指南
            show_usage_guide()
            
            print("\n🔥 系统现在完全由DeepSeek R1驱动!")
            print("   享受强大推理能力带来的专业级投资分析吧!")
            
        else:
            print(f"\n⚠️  还有问题需要解决")
            print(f"   配置测试: {tests_passed}/{total_tests}")
            print(f"   分析测试: {'通过' if analysis_success else '失败'}")
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
