#!/usr/bin/env python3
"""
修复后的DeepSeek R1配置测试脚本
验证API密钥配置问题是否已解决
"""

import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 已加载 .env 文件")
except ImportError:
    # 手动加载.env文件
    env_path = '.env'
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
        print("✅ 手动加载 .env 文件成功")

def test_environment_variables():
    """测试环境变量是否正确加载"""
    print("\n🔍 测试环境变量加载")
    print("=" * 50)
    
    # 检查所有API密钥
    api_keys = {
        "OPENAI_API_KEY": "OpenAI",
        "ANTHROPIC_API_KEY": "Anthropic",
        "GOOGLE_API_KEY": "Google",
        "GROQ_API_KEY": "Groq",
        "DEEPSEEK_API_KEY": "DeepSeek"
    }
    
    loaded_keys = {}
    
    for env_var, provider in api_keys.items():
        value = os.getenv(env_var)
        if value:
            print(f"✅ {provider}: {env_var} = {value[:10]}...")
            loaded_keys[provider] = True
        else:
            print(f"❌ {provider}: {env_var} 未设置")
            loaded_keys[provider] = False
    
    return loaded_keys

def test_deepseek_api_key():
    """测试DeepSeek API密钥"""
    print("\n🔑 测试DeepSeek API密钥")
    print("=" * 50)
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if api_key and api_key.startswith("sk-"):
        print(f"✅ DeepSeek API密钥已正确配置: {api_key[:10]}...")
        return True
    else:
        print("❌ DeepSeek API密钥未配置或格式错误")
        if api_key:
            print(f"   当前值: {api_key}")
        return False

def test_default_model_config():
    """测试默认模型配置"""
    print("\n🎯 测试默认模型配置")
    print("=" * 50)
    
    try:
        from utils.llm import get_agent_model_config
        
        # 模拟空状态，应该使用默认配置
        class MockState:
            def get(self, key, default=None):
                if key == "metadata":
                    return {}
                return default
        
        state = MockState()
        model_name, model_provider = get_agent_model_config(state, None)
        
        print(f"📊 默认模型: {model_name}")
        print(f"📊 默认提供商: {model_provider}")
        
        # 验证是否为DeepSeek R1
        if model_name == "deepseek-reasoner" and model_provider == "DEEPSEEK":
            print("✅ 默认模型已正确设置为 DeepSeek R1")
            return True
        else:
            print("❌ 默认模型配置不正确")
            print(f"   期望: deepseek-reasoner (DEEPSEEK)")
            print(f"   实际: {model_name} ({model_provider})")
            return False
            
    except Exception as e:
        print(f"❌ 默认模型测试失败: {str(e)}")
        return False

def test_deepseek_model_initialization():
    """测试DeepSeek模型初始化"""
    print("\n🤖 测试DeepSeek模型初始化")
    print("=" * 50)
    
    try:
        from llm.models import get_model, ModelProvider
        
        # 检查API密钥
        api_key = os.getenv("DEEPSEEK_API_KEY")
        if not api_key:
            print("❌ DeepSeek API密钥未设置")
            return False
        
        print(f"🔍 使用API密钥: {api_key[:10]}...")
        
        # 尝试初始化DeepSeek R1模型
        model = get_model("deepseek-reasoner", ModelProvider.DEEPSEEK)
        
        if model:
            print("✅ DeepSeek R1 模型初始化成功")
            print(f"   模型类型: {type(model)}")
            return True
        else:
            print("❌ DeepSeek R1 模型初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ DeepSeek模型初始化错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_deepseek_call():
    """测试DeepSeek实际调用"""
    print("\n🧪 测试DeepSeek R1实际调用")
    print("=" * 50)
    
    try:
        from llm.models import get_model, ModelProvider
        
        # 初始化模型
        model = get_model("deepseek-reasoner", ModelProvider.DEEPSEEK)
        
        if not model:
            print("❌ 模型初始化失败")
            return False
        
        print("✅ 模型初始化成功，开始测试调用...")
        
        # 简单测试调用
        response = model.invoke("请用一句话简单介绍苹果公司(AAPL)的投资价值。")
        print(f"🤖 DeepSeek R1 回答: {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek调用失败: {str(e)}")
        return False

def test_all_models():
    """测试所有模型的初始化"""
    print("\n🔧 测试所有模型初始化")
    print("=" * 50)
    
    try:
        from llm.models import get_model, ModelProvider
        
        # 测试所有配置的模型
        test_models = [
            ("gpt-4o", ModelProvider.OPENAI, "OpenAI GPT-4o"),
            ("claude-3-5-haiku-latest", ModelProvider.ANTHROPIC, "Anthropic Claude"),
            ("gemini-1.5-flash-latest", ModelProvider.GEMINI, "Google Gemini"),
            ("deepseek-reasoner", ModelProvider.DEEPSEEK, "DeepSeek R1")
        ]
        
        results = {}
        
        for model_name, provider, description in test_models:
            try:
                print(f"🔄 测试 {description}")
                
                # 检查对应的API密钥
                api_key_map = {
                    ModelProvider.OPENAI: "OPENAI_API_KEY",
                    ModelProvider.ANTHROPIC: "ANTHROPIC_API_KEY",
                    ModelProvider.GEMINI: "GOOGLE_API_KEY",
                    ModelProvider.DEEPSEEK: "DEEPSEEK_API_KEY"
                }
                
                api_key = os.getenv(api_key_map[provider])
                if not api_key:
                    print(f"⚠️  跳过 {description} - API密钥未配置")
                    results[description] = "未配置"
                    continue
                
                # 尝试初始化模型
                model = get_model(model_name, provider)
                if model:
                    print(f"✅ {description} 初始化成功")
                    results[description] = "成功"
                else:
                    print(f"❌ {description} 初始化失败")
                    results[description] = "失败"
                    
            except Exception as e:
                print(f"❌ {description} 初始化错误: {str(e)}")
                results[description] = f"错误: {str(e)[:50]}..."
        
        return results
        
    except Exception as e:
        print(f"❌ 模型测试失败: {str(e)}")
        return {}

def main():
    """主测试函数"""
    print("🔧 DeepSeek R1 配置修复验证")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("🎯 目标: 验证API密钥配置问题是否已修复")
    
    # 运行测试
    tests_passed = 0
    total_tests = 5
    
    try:
        # 1. 测试环境变量
        env_vars = test_environment_variables()
        if env_vars.get("DeepSeek", False):
            tests_passed += 1
        
        # 2. 测试DeepSeek API密钥
        if test_deepseek_api_key():
            tests_passed += 1
        
        # 3. 测试默认模型配置
        if test_default_model_config():
            tests_passed += 1
        
        # 4. 测试DeepSeek模型初始化
        if test_deepseek_model_initialization():
            tests_passed += 1
        
        # 5. 测试DeepSeek实际调用
        if test_deepseek_call():
            tests_passed += 1
        
        # 额外测试：所有模型
        print("\n" + "="*60)
        all_models_results = test_all_models()
        
        # 总结
        print(f"\n📊 修复验证总结")
        print("=" * 40)
        print(f"✅ 通过测试: {tests_passed}/{total_tests}")
        
        if tests_passed >= 4:
            print("\n🎉 DeepSeek R1 配置修复成功!")
            print("\n✅ API密钥配置问题已解决")
            print("✅ 模型初始化正常工作")
            print("✅ 系统默认使用 DeepSeek R1")
            print("✅ 所有功能测试通过")
            
            print("\n🔧 现在可以正常使用:")
            print("  1. python src/main.py --ticker AAPL")
            print("  2. cd app && ./run.sh")
            print("  3. 享受DeepSeek R1的强大推理分析!")
        else:
            print(f"\n⚠️  还有 {total_tests - tests_passed} 个测试未通过")
            print("请检查具体错误信息")
        
        # 显示所有模型状态
        if all_models_results:
            print(f"\n📋 所有模型状态:")
            for model, status in all_models_results.items():
                status_icon = "✅" if status == "成功" else "❌" if "错误" in status else "⚠️"
                print(f"   {status_icon} {model}: {status}")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
