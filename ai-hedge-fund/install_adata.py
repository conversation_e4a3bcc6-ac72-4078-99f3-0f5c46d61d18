#!/usr/bin/env python3
"""
Installation script for AData library
Installs AData for Chinese A-share market data support
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"   Error: {e.stderr.strip()}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    print("🔍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 7:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible")
        print("   AData requires Python 3.7 or higher")
        return False

def install_adata():
    """Install AData library"""
    print("\n🚀 Installing AData for Chinese A-share market data...")
    
    # Check Python version first
    if not check_python_version():
        return False
    
    # Try different installation methods
    installation_commands = [
        ("pip install adata", "Installing AData with pip"),
        ("pip install adata -i http://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com", "Installing AData with Aliyun mirror"),
        ("pip install adata -i https://pypi.tuna.tsinghua.edu.cn/simple", "Installing AData with Tsinghua mirror")
    ]
    
    for command, description in installation_commands:
        if run_command(command, description):
            break
    else:
        print("❌ All installation attempts failed")
        return False
    
    # Verify installation
    print("\n🔍 Verifying AData installation...")
    try:
        import adata
        print("✅ AData imported successfully")
        
        # Test basic functionality
        print("🧪 Testing basic AData functionality...")
        try:
            # Try to get stock list (this will test if AData is working)
            df = adata.stock.info.all_code()
            stock_count = len(df)
            print(f"✅ AData is working! Found {stock_count} Chinese stocks")
            
            # Show some sample stocks
            print("\n📊 Sample Chinese stocks:")
            for i, row in df.head(5).iterrows():
                print(f"   {row['stock_code']} - {row['short_name']} ({row['exchange']})")
            
            return True
            
        except Exception as e:
            print(f"❌ AData functionality test failed: {str(e)}")
            print("   AData is installed but may not be working properly")
            return False
            
    except ImportError as e:
        print(f"❌ AData import failed: {str(e)}")
        return False

def update_requirements():
    """Update requirements file to include AData"""
    print("\n📝 Updating project requirements...")
    
    # Check if pyproject.toml exists
    pyproject_path = "pyproject.toml"
    if os.path.exists(pyproject_path):
        print(f"✅ Found {pyproject_path}")
        
        # Read current content
        with open(pyproject_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if adata is already in dependencies
        if 'adata' not in content:
            print("📝 Adding AData to pyproject.toml dependencies...")
            
            # Find the dependencies section
            lines = content.split('\n')
            new_lines = []
            in_dependencies = False
            
            for line in lines:
                new_lines.append(line)
                
                # Check if we're in the dependencies section
                if line.strip() == '[tool.poetry.dependencies]':
                    in_dependencies = True
                elif line.startswith('[') and in_dependencies:
                    # We've left the dependencies section, add adata before this line
                    new_lines.insert(-1, 'adata = "^0.12.0"  # Chinese A-share market data')
                    in_dependencies = False
            
            # If we're still in dependencies (end of file), add it
            if in_dependencies:
                new_lines.append('adata = "^0.12.0"  # Chinese A-share market data')
            
            # Write back to file
            with open(pyproject_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(new_lines))
            
            print("✅ Added AData to pyproject.toml")
        else:
            print("✅ AData is already in pyproject.toml")
    else:
        print("⚠️  pyproject.toml not found, skipping requirements update")

def main():
    """Main installation function"""
    print("🇨🇳 AData Installation for Chinese A-Share Market Support")
    print("=" * 60)
    
    # Install AData
    if install_adata():
        print("\n🎉 AData installation completed successfully!")
        
        # Update requirements
        update_requirements()
        
        print("\n✅ Setup complete! You can now:")
        print("   • Analyze Chinese A-share stocks (000001, 600000, 300001, etc.)")
        print("   • Get real-time and historical price data")
        print("   • Access financial metrics and company information")
        print("   • Use dividend and market cap data")
        
        print("\n🧪 To test the integration, run:")
        print("   python test_adata_integration.py")
        
        print("\n📚 AData Documentation:")
        print("   https://adata.30006124.xyz/")
        
    else:
        print("\n❌ AData installation failed!")
        print("\n🔧 Manual installation steps:")
        print("   1. pip install adata")
        print("   2. If that fails, try with a Chinese mirror:")
        print("      pip install adata -i http://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com")
        print("   3. Test with: python -c 'import adata; print(\"AData works!\")'")
        
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
