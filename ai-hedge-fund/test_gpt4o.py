#!/usr/bin/env python3
"""
简单测试GPT-4o模型
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_gpt4o():
    """测试GPT-4o模型"""
    print("🧪 测试GPT-4o模型")
    print("=" * 50)
    
    # 检查API密钥
    api_key = os.getenv("OPENAI_API_KEY")
    print(f"API密钥: {api_key[:10]}..." if api_key else "❌ API密钥未设置")
    
    if not api_key:
        print("❌ 请在.env文件中设置OPENAI_API_KEY")
        return
    
    try:
        from langchain_openai import ChatOpenAI
        
        # 初始化模型
        model = ChatOpenAI(
            model="gpt-4o",
            api_key=api_key,
            temperature=0.1
        )
        
        print("✅ GPT-4o模型初始化成功")
        
        # 简单测试
        response = model.invoke("请简单介绍一下苹果公司(AAPL)的投资价值，用一句话回答。")
        print(f"🤖 GPT-4o回答: {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ GPT-4o测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    test_gpt4o()
