#!/usr/bin/env python3
"""
A股市场股票分析测试脚本
使用DeepSeek R1分析指定的A股股票
"""

import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 已加载 .env 文件")
except ImportError:
    # 手动加载.env文件
    env_path = '.env'
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
        print("✅ 手动加载 .env 文件成功")

def analyze_stock_with_deepseek(stock_code, stock_name):
    """使用DeepSeek R1分析单只A股"""
    print(f"\n🔍 分析 {stock_code} - {stock_name}")
    print("=" * 60)
    
    try:
        from langchain_deepseek import ChatDeepSeek
        
        api_key = os.getenv("DEEPSEEK_API_KEY")
        if not api_key:
            print("❌ DeepSeek API密钥未设置")
            return False
        
        # 初始化DeepSeek R1模型
        model = ChatDeepSeek(
            model="deepseek-reasoner",
            api_key=api_key,
            temperature=0.1
        )
        
        # 构建分析提示
        prompt = f"""
        请深度分析A股股票 {stock_code} ({stock_name}) 的投资价值：

        请从以下角度进行专业分析：
        1. 公司基本面分析（主营业务、行业地位、竞争优势）
        2. 财务状况分析（盈利能力、成长性、财务健康度）
        3. 行业前景分析（行业趋势、政策影响、市场空间）
        4. 技术面分析（股价走势、估值水平、技术指标）
        5. 风险因素分析（主要风险点、不确定性因素）
        6. 投资建议（买入/持有/卖出，目标价位，投资逻辑）

        请用中文进行详细分析，并在最后给出明确的投资建议和理由。
        """
        
        print("🤖 DeepSeek R1 正在进行深度分析...")
        response = model.invoke(prompt)
        
        print("📊 分析结果:")
        print("-" * 50)
        print(response.content)
        print("-" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        return False

def get_stock_info():
    """获取股票基本信息"""
    stocks_info = {
        "000062": {
            "name": "深圳华强",
            "industry": "电子信息",
            "description": "电子元器件分销、电子市场运营"
        },
        "300120": {
            "name": "经纬辉开", 
            "industry": "纺织服装",
            "description": "纺织机械制造"
        },
        "002428": {
            "name": "云南锗业",
            "industry": "有色金属",
            "description": "锗系列产品生产"
        }
    }
    return stocks_info

def analyze_portfolio_with_deepseek(stocks):
    """使用DeepSeek R1进行投资组合分析"""
    print(f"\n🎯 投资组合综合分析")
    print("=" * 60)
    
    try:
        from langchain_deepseek import ChatDeepSeek
        
        api_key = os.getenv("DEEPSEEK_API_KEY")
        model = ChatDeepSeek(
            model="deepseek-reasoner",
            api_key=api_key,
            temperature=0.1
        )
        
        # 构建组合分析提示
        stocks_list = ", ".join([f"{code}({info['name']})" for code, info in stocks.items()])
        
        prompt = f"""
        请对以下A股投资组合进行综合分析：
        {stocks_list}

        请从以下角度进行分析：
        1. 行业分布分析（行业配置是否合理，是否过于集中）
        2. 风险分散效果（不同股票间的相关性，风险对冲效果）
        3. 成长性与价值性平衡（成长股与价值股的配置）
        4. 市场周期适应性（在不同市场环境下的表现预期）
        5. 投资组合优化建议（权重配置建议，调整方向）
        6. 整体投资策略（持有期建议，风险控制措施）

        请用中文进行专业分析，并给出具体的投资组合管理建议。
        """
        
        print("🤖 DeepSeek R1 正在进行投资组合分析...")
        response = model.invoke(prompt)
        
        print("📈 投资组合分析结果:")
        print("-" * 50)
        print(response.content)
        print("-" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ 投资组合分析失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🇨🇳 A股市场股票分析 - DeepSeek R1")
    print("=" * 80)
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 要分析的股票
    target_stocks = ["000062", "300120", "002428"]
    stocks_info = get_stock_info()
    
    print("📋 分析目标:")
    for stock_code in target_stocks:
        info = stocks_info[stock_code]
        print(f"   • {stock_code} - {info['name']} ({info['industry']})")
        print(f"     {info['description']}")
    
    print(f"\n🎯 使用模型: DeepSeek R1 (deepseek-reasoner)")
    print("🌟 优势: 强大推理能力 + 中文理解优秀 + 成本极低")
    
    # 分析每只股票
    successful_analyses = 0
    
    for stock_code in target_stocks:
        info = stocks_info[stock_code]
        if analyze_stock_with_deepseek(stock_code, info['name']):
            successful_analyses += 1
        
        # 添加分隔符
        print("\n" + "="*80)
    
    # 投资组合分析
    if successful_analyses > 0:
        analyze_portfolio_with_deepseek(stocks_info)
    
    # 总结
    print(f"\n📊 分析总结")
    print("=" * 40)
    print(f"✅ 成功分析: {successful_analyses}/{len(target_stocks)} 只股票")
    
    if successful_analyses == len(target_stocks):
        print("\n🎉 所有A股分析完成!")
        print("\n💡 DeepSeek R1分析特点:")
        print("   • 🧠 深度推理: 多角度专业分析")
        print("   • 🇨🇳 中文优势: 理解A股市场特色")
        print("   • 📊 结构化: 系统性投资分析框架")
        print("   • 💰 成本低: 高性价比的AI分析")
        
        print("\n📈 投资建议:")
        print("   • 以上分析仅供参考，不构成投资建议")
        print("   • 请结合自身风险承受能力做出投资决策")
        print("   • 建议关注公司基本面变化和行业发展趋势")
        print("   • 注意控制仓位和分散投资风险")
    else:
        print(f"\n⚠️  还有 {len(target_stocks) - successful_analyses} 只股票分析失败")
        print("请检查网络连接和API配置")

if __name__ == "__main__":
    main()
