#!/usr/bin/env python3
"""
测试Gemini 2.5 Pro作为默认模型
验证系统是否正确使用Gemini 2.5 Pro进行分析
"""

import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 已加载 .env 文件")
except ImportError:
    # 手动加载.env文件
    env_path = '.env'
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
        print("✅ 手动加载 .env 文件成功")

def test_gemini_api_key():
    """测试Gemini API密钥"""
    print("\n🔑 测试Gemini API密钥")
    print("=" * 50)
    
    api_key = os.getenv("GOOGLE_API_KEY")
    if api_key and api_key.startswith("AIza"):
        print(f"✅ Gemini API密钥已配置: {api_key[:10]}...")
        return True
    else:
        print("❌ Gemini API密钥未配置或格式错误")
        return False

def test_default_model_config():
    """测试默认模型配置"""
    print("\n🎯 测试默认模型配置")
    print("=" * 50)
    
    try:
        from utils.llm import get_agent_model_config
        
        # 模拟空状态，应该使用默认配置
        class MockState:
            def get(self, key, default=None):
                if key == "metadata":
                    return {}
                return default
        
        state = MockState()
        model_name, model_provider = get_agent_model_config(state, None)
        
        print(f"📊 默认模型: {model_name}")
        print(f"📊 默认提供商: {model_provider}")
        
        # 验证是否为Gemini 2.5 Pro
        if model_name == "gemini-2.5-pro-preview-06-05" and model_provider == "GEMINI":
            print("✅ 默认模型已正确设置为 Gemini 2.5 Pro")
            return True
        else:
            print("❌ 默认模型配置不正确")
            return False
            
    except Exception as e:
        print(f"❌ 默认模型测试失败: {str(e)}")
        return False

def test_gemini_model_initialization():
    """测试Gemini模型初始化"""
    print("\n🤖 测试Gemini模型初始化")
    print("=" * 50)
    
    try:
        from llm.models import get_model, ModelProvider
        
        # 检查API密钥
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            print("❌ Google API密钥未设置")
            return False
        
        # 尝试初始化Gemini 2.5 Pro模型
        model = get_model("gemini-2.5-pro-preview-06-05", ModelProvider.GEMINI)
        
        if model:
            print("✅ Gemini 2.5 Pro 模型初始化成功")
            return True
        else:
            print("❌ Gemini 2.5 Pro 模型初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ Gemini模型初始化错误: {str(e)}")
        return False

def test_simple_gemini_call():
    """测试简单的Gemini调用"""
    print("\n🧪 测试Gemini 2.5 Pro调用")
    print("=" * 50)
    
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            print("❌ Google API密钥未设置")
            return False
        
        # 初始化Gemini模型
        model = ChatGoogleGenerativeAI(
            model="gemini-2.5-pro-preview-06-05",
            api_key=api_key,
            temperature=0.1
        )
        
        print("✅ Gemini 2.5 Pro 初始化成功")
        
        # 简单测试调用
        response = model.invoke("请用一句话简单介绍苹果公司(AAPL)的投资价值。")
        print(f"🤖 Gemini 2.5 Pro 回答: {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ Gemini调用失败: {str(e)}")
        return False

def show_gemini_usage_examples():
    """显示使用Gemini的示例"""
    print("\n💡 Gemini 2.5 Pro 使用示例")
    print("=" * 60)
    
    examples = [
        {
            "description": "默认使用Gemini 2.5 Pro分析股票",
            "command": "python src/main.py --ticker AAPL",
            "note": "系统现在默认使用Gemini 2.5 Pro"
        },
        {
            "description": "分析多只美股",
            "command": "python src/main.py --ticker AAPL,MSFT,GOOGL",
            "note": "使用Gemini 2.5 Pro分析多只股票"
        },
        {
            "description": "分析中国A股",
            "command": "python src/main.py --ticker 000001,600036,000858",
            "note": "Gemini对中文理解也很好"
        },
        {
            "description": "混合中美股票分析",
            "command": "python src/main.py --ticker AAPL,000001,MSFT,600036",
            "note": "同时分析中美股票市场"
        },
        {
            "description": "明确指定Gemini模型",
            "command": "python src/main.py --ticker AAPL --model gemini-2.5-pro-preview-06-05 --provider Gemini",
            "note": "显式指定使用Gemini 2.5 Pro"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}️⃣ {example['description']}")
        print(f"   命令: {example['command']}")
        print(f"   说明: {example['note']}")

def show_gemini_advantages():
    """显示Gemini 2.5 Pro的优势"""
    print("\n🌟 Gemini 2.5 Pro 优势")
    print("=" * 60)
    
    advantages = [
        "🚀 **高性能**: Google最新的高性能模型",
        "💰 **成本效益**: 相比GPT-4o和Claude，成本更低",
        "⚡ **响应速度**: 推理速度快，适合实时分析",
        "🌐 **多语言**: 对中英文都有很好的理解能力",
        "🔍 **多模态**: 支持文本、图像等多种输入",
        "📊 **数据处理**: 擅长处理结构化数据和复杂分析",
        "🎯 **准确性**: 在金融分析任务上表现优秀",
        "🔄 **稳定性**: Google云基础设施保证高可用性"
    ]
    
    for advantage in advantages:
        print(f"   {advantage}")

def main():
    """主测试函数"""
    print("🚀 Gemini 2.5 Pro 默认模型配置测试")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("🎯 目标: 将系统默认模型设置为 Gemini 2.5 Pro")
    
    # 运行测试
    tests_passed = 0
    total_tests = 4
    
    try:
        # 1. 测试API密钥
        if test_gemini_api_key():
            tests_passed += 1
        
        # 2. 测试默认模型配置
        if test_default_model_config():
            tests_passed += 1
        
        # 3. 测试模型初始化
        if test_gemini_model_initialization():
            tests_passed += 1
        
        # 4. 测试实际调用
        if test_simple_gemini_call():
            tests_passed += 1
        
        # 显示使用示例
        show_gemini_usage_examples()
        
        # 显示优势
        show_gemini_advantages()
        
        # 总结
        print(f"\n📊 测试总结")
        print("=" * 40)
        print(f"✅ 通过测试: {tests_passed}/{total_tests}")
        
        if tests_passed == total_tests:
            print("\n🎉 Gemini 2.5 Pro 配置完全成功!")
            print("\n✅ 系统现在将默认使用 Gemini 2.5 Pro 进行所有分析")
            print("✅ 所有AI代理都将使用 Gemini 2.5 Pro")
            print("✅ 支持中美股票混合分析")
            print("✅ 高性能、低成本、快速响应")
            
            print("\n🔧 下一步:")
            print("  1. 运行: python src/main.py --ticker AAPL")
            print("  2. 或启动Web界面: cd app && ./run.sh")
            print("  3. 享受Gemini 2.5 Pro的高性能分析!")
        else:
            print(f"\n⚠️  还有 {total_tests - tests_passed} 个测试未通过")
            print("请检查配置并重试")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
