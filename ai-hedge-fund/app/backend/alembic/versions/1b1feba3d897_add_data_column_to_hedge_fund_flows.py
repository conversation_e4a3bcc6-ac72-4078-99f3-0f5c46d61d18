"""add_data_column_to_hedge_fund_flows

Revision ID: 1b1feba3d897
Revises: 5274886e5bee
Create Date: 2025-06-22 17:30:50.992184

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1b1feba3d897'
down_revision: Union[str, None] = '5274886e5bee'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('hedge_fund_flows', sa.Column('data', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('hedge_fund_flows', 'data')
    # ### end Alembic commands ###
