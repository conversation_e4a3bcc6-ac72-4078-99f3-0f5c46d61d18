#!/usr/bin/env python3
"""
Test script for AData integration
Tests the new AData adapter functionality for Chinese A-share stocks
"""

import sys
import os
from datetime import datetime, timedel<PERSON>

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from tools.adata_adapter import (
    is_chinese_stock, 
    ADATA_AVAILABLE,
    get_all_chinese_stocks
)
from tools.api import get_prices, get_financial_metrics, get_market_cap

def test_chinese_stock_detection():
    """Test Chinese stock ticker detection"""
    print("🧪 Testing Chinese stock detection...")
    
    # Test Chinese stocks
    chinese_tickers = ['000001', '600000', '300001', '688001']
    for ticker in chinese_tickers:
        result = is_chinese_stock(ticker)
        print(f"  {ticker}: {'✅' if result else '❌'} Chinese stock")
        assert result, f"{ticker} should be detected as Chinese stock"
    
    # Test US stocks
    us_tickers = ['AAPL', 'GOOGL', 'MSFT', 'TSLA']
    for ticker in us_tickers:
        result = is_chinese_stock(ticker)
        print(f"  {ticker}: {'❌' if not result else '✅'} US stock")
        assert not result, f"{ticker} should not be detected as Chinese stock"
    
    print("✅ Chinese stock detection test passed!\n")


def test_adata_availability():
    """Test AData library availability"""
    print("🧪 Testing AData availability...")
    
    if ADATA_AVAILABLE:
        print("✅ AData is available and ready to use")
        try:
            # Test getting Chinese stock list
            stocks = get_all_chinese_stocks()
            print(f"✅ Found {len(stocks)} Chinese stocks")
            
            # Show first 5 stocks
            print("📊 Sample Chinese stocks:")
            for i, stock in enumerate(stocks[:5]):
                print(f"  {i+1}. {stock['ticker']} - {stock['name']} ({stock['exchange']})")
        except Exception as e:
            print(f"❌ Error testing AData: {str(e)}")
    else:
        print("❌ AData is not available. Please install with: pip install adata")
    
    print()


def test_price_data_integration():
    """Test price data fetching with AData integration"""
    print("🧪 Testing price data integration...")
    
    # Test Chinese stock (if AData is available)
    if ADATA_AVAILABLE:
        try:
            chinese_ticker = "000001"  # Ping An Bank
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            
            print(f"  Fetching {chinese_ticker} price data from {start_date} to {end_date}...")
            prices = get_prices(chinese_ticker, start_date, end_date)
            
            if prices:
                print(f"✅ Successfully fetched {len(prices)} price records for {chinese_ticker}")
                latest_price = prices[-1]
                print(f"  Latest price: {latest_price.close} CNY (Date: {latest_price.time})")
            else:
                print(f"❌ No price data found for {chinese_ticker}")
                
        except Exception as e:
            print(f"❌ Error fetching Chinese stock price data: {str(e)}")
    
    # Test US stock (using Financial Datasets API)
    try:
        us_ticker = "AAPL"
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        print(f"  Fetching {us_ticker} price data from {start_date} to {end_date}...")
        prices = get_prices(us_ticker, start_date, end_date)
        
        if prices:
            print(f"✅ Successfully fetched {len(prices)} price records for {us_ticker}")
            latest_price = prices[-1]
            print(f"  Latest price: ${latest_price.close} USD (Date: {latest_price.time})")
        else:
            print(f"❌ No price data found for {us_ticker}")
            
    except Exception as e:
        print(f"❌ Error fetching US stock price data: {str(e)}")
    
    print()


def test_financial_metrics_integration():
    """Test financial metrics fetching with AData integration"""
    print("🧪 Testing financial metrics integration...")
    
    # Test Chinese stock (if AData is available)
    if ADATA_AVAILABLE:
        try:
            chinese_ticker = "000001"  # Ping An Bank
            end_date = datetime.now().strftime('%Y-%m-%d')
            
            print(f"  Fetching {chinese_ticker} financial metrics...")
            metrics = get_financial_metrics(chinese_ticker, end_date)
            
            if metrics:
                print(f"✅ Successfully fetched {len(metrics)} financial metric records for {chinese_ticker}")
                latest_metric = metrics[0]
                print(f"  ROE: {latest_metric.return_on_equity}%")
                print(f"  ROA: {latest_metric.return_on_assets}%")
                print(f"  Current Ratio: {latest_metric.current_ratio}")
            else:
                print(f"❌ No financial metrics found for {chinese_ticker}")
                
        except Exception as e:
            print(f"❌ Error fetching Chinese stock financial metrics: {str(e)}")
    
    print()


def test_market_cap_integration():
    """Test market cap calculation with AData integration"""
    print("🧪 Testing market cap integration...")
    
    # Test Chinese stock (if AData is available)
    if ADATA_AVAILABLE:
        try:
            chinese_ticker = "000001"  # Ping An Bank
            end_date = datetime.now().strftime('%Y-%m-%d')
            
            print(f"  Calculating {chinese_ticker} market cap...")
            market_cap = get_market_cap(chinese_ticker, end_date)
            
            if market_cap:
                market_cap_billion = market_cap / 1_000_000_000
                print(f"✅ {chinese_ticker} market cap: ¥{market_cap_billion:.2f} billion")
            else:
                print(f"❌ Could not calculate market cap for {chinese_ticker}")
                
        except Exception as e:
            print(f"❌ Error calculating Chinese stock market cap: {str(e)}")
    
    print()


def main():
    """Run all tests"""
    print("🚀 Starting AData Integration Tests\n")
    print("=" * 50)
    
    # Run tests
    test_chinese_stock_detection()
    test_adata_availability()
    
    if ADATA_AVAILABLE:
        test_price_data_integration()
        test_financial_metrics_integration()
        test_market_cap_integration()
    else:
        print("⚠️  Skipping AData-specific tests because AData is not installed")
        print("   To install AData, run: pip install adata")
    
    print("=" * 50)
    print("🎉 All tests completed!")
    
    if ADATA_AVAILABLE:
        print("\n✅ AData integration is working correctly!")
        print("   You can now analyze Chinese A-share stocks alongside US stocks.")
    else:
        print("\n⚠️  To enable Chinese A-share analysis, please install AData:")
        print("   pip install adata")


if __name__ == "__main__":
    main()
