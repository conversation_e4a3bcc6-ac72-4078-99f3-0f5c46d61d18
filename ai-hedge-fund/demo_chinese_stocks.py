#!/usr/bin/env python3
"""
Demo script showing Chinese A-share stock analysis with AData integration
Demonstrates the AI hedge fund system analyzing Chinese stocks
"""

import sys
import os
from datetime import datetime, timedelta

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from tools.api import get_prices, get_financial_metrics, get_market_cap
from tools.adata_adapter import get_all_chinese_stocks, get_adata_dividend_info

def analyze_chinese_stock(ticker: str):
    """Analyze a Chinese A-share stock"""
    print(f"\n📊 Analyzing Chinese Stock: {ticker}")
    print("=" * 50)
    
    try:
        # Get basic info
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        # 1. Price Data
        print("📈 Price Data:")
        prices = get_prices(ticker, start_date, end_date)
        if prices:
            latest_price = prices[-1]
            first_price = prices[0]
            price_change = ((float(latest_price.close) - float(first_price.close)) / float(first_price.close)) * 100
            
            print(f"  Current Price: ¥{latest_price.close}")
            print(f"  30-day Change: {price_change:+.2f}%")
            print(f"  Volume: {latest_price.volume:,}")
            print(f"  Date Range: {first_price.time} to {latest_price.time}")
        else:
            print("  ❌ No price data available")
        
        # 2. Financial Metrics
        print("\n💰 Financial Metrics:")
        metrics = get_financial_metrics(ticker, end_date)
        if metrics:
            metric = metrics[0]
            print(f"  ROE: {metric.return_on_equity:.2f}%" if metric.return_on_equity else "  ROE: N/A")
            print(f"  ROA: {metric.return_on_assets:.2f}%" if metric.return_on_assets else "  ROA: N/A")
            print(f"  Current Ratio: {metric.current_ratio:.2f}" if metric.current_ratio else "  Current Ratio: N/A")
            print(f"  Quick Ratio: {metric.quick_ratio:.2f}" if metric.quick_ratio else "  Quick Ratio: N/A")
            print(f"  Gross Margin: {metric.gross_margin:.2f}%" if metric.gross_margin else "  Gross Margin: N/A")
            print(f"  Net Margin: {metric.net_margin:.2f}%" if metric.net_margin else "  Net Margin: N/A")
            print(f"  Revenue Growth: {metric.revenue_growth:.2f}%" if metric.revenue_growth else "  Revenue Growth: N/A")
            print(f"  EPS: ¥{metric.earnings_per_share:.2f}" if metric.earnings_per_share else "  EPS: N/A")
        else:
            print("  ❌ No financial metrics available")
        
        # 3. Market Cap
        print("\n🏢 Market Capitalization:")
        market_cap = get_market_cap(ticker, end_date)
        if market_cap:
            market_cap_billion = market_cap / 1_000_000_000
            print(f"  Market Cap: ¥{market_cap_billion:.2f} billion")
        else:
            print("  ❌ Market cap not available")
        
        # 4. Dividend Info
        print("\n💵 Dividend Information:")
        try:
            dividends = get_adata_dividend_info(ticker)
            if dividends:
                print(f"  Found {len(dividends)} dividend records")
                for i, div in enumerate(dividends[:3]):  # Show first 3
                    print(f"  {i+1}. Report Date: {div['report_date']}, Ex-Date: {div['ex_dividend_date']}")
                    print(f"     Plan: {div['dividend_plan']}")
            else:
                print("  ❌ No dividend information available")
        except Exception as e:
            print(f"  ❌ Error fetching dividend info: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing {ticker}: {str(e)}")
        return False

def demo_popular_chinese_stocks():
    """Demo analysis of popular Chinese stocks"""
    print("🇨🇳 AI Hedge Fund - Chinese A-Share Stock Analysis Demo")
    print("=" * 60)
    
    # Popular Chinese stocks to analyze
    popular_stocks = [
        ("000001", "平安银行 - Ping An Bank"),
        ("000002", "万科A - China Vanke"),
        ("600000", "浦发银行 - Shanghai Pudong Development Bank"),
        ("600036", "招商银行 - China Merchants Bank"),
        ("000858", "五粮液 - Wuliangye Yibin"),
    ]
    
    print(f"📋 Analyzing {len(popular_stocks)} popular Chinese A-share stocks...")
    
    successful_analyses = 0
    for ticker, name in popular_stocks:
        print(f"\n🔍 {name}")
        if analyze_chinese_stock(ticker):
            successful_analyses += 1
        
        # Add a small delay between analyses
        import time
        time.sleep(1)
    
    print(f"\n✅ Successfully analyzed {successful_analyses}/{len(popular_stocks)} stocks")

def demo_stock_screening():
    """Demo stock screening functionality"""
    print("\n🔍 Chinese Stock Screening Demo")
    print("=" * 40)
    
    try:
        # Get all Chinese stocks
        all_stocks = get_all_chinese_stocks()
        print(f"📊 Total Chinese stocks available: {len(all_stocks)}")
        
        # Filter by exchange
        sz_stocks = [s for s in all_stocks if s['exchange'] == 'SZ']
        sh_stocks = [s for s in all_stocks if s['exchange'] == 'SH']
        
        print(f"  Shenzhen Exchange (SZ): {len(sz_stocks)} stocks")
        print(f"  Shanghai Exchange (SH): {len(sh_stocks)} stocks")
        
        # Show some examples from each exchange
        print("\n📋 Sample Shenzhen Stocks:")
        for i, stock in enumerate(sz_stocks[:5]):
            print(f"  {i+1}. {stock['ticker']} - {stock['name']}")
        
        print("\n📋 Sample Shanghai Stocks:")
        for i, stock in enumerate(sh_stocks[:5]):
            print(f"  {i+1}. {stock['ticker']} - {stock['name']}")
        
    except Exception as e:
        print(f"❌ Error in stock screening: {str(e)}")

def demo_mixed_portfolio():
    """Demo mixed US and Chinese stock portfolio"""
    print("\n🌍 Mixed Portfolio Demo (US + Chinese Stocks)")
    print("=" * 50)
    
    mixed_portfolio = [
        ("AAPL", "Apple Inc. (US)"),
        ("000001", "平安银行 (China)"),
        ("MSFT", "Microsoft Corp. (US)"),
        ("600036", "招商银行 (China)"),
        ("TSLA", "Tesla Inc. (US)"),
        ("000858", "五粮液 (China)")
    ]
    
    print("📊 Portfolio Analysis:")
    for ticker, name in mixed_portfolio:
        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            
            prices = get_prices(ticker, start_date, end_date)
            if prices:
                latest_price = prices[-1]
                currency = "¥" if ticker.isdigit() else "$"
                print(f"  {name}: {currency}{latest_price.close}")
            else:
                print(f"  {name}: Price not available")
        except Exception as e:
            print(f"  {name}: Error - {str(e)}")

def main():
    """Main demo function"""
    print("🚀 Starting Chinese A-Share Integration Demo\n")
    
    # Check if AData is available
    from tools.adata_adapter import ADATA_AVAILABLE
    if not ADATA_AVAILABLE:
        print("❌ AData is not available. Please install with: pip install adata")
        return
    
    try:
        # Run demos
        demo_popular_chinese_stocks()
        demo_stock_screening()
        demo_mixed_portfolio()
        
        print("\n🎉 Demo completed successfully!")
        print("\n💡 Next Steps:")
        print("  • Run the full AI hedge fund analysis with Chinese stocks")
        print("  • Use command: python src/main.py --ticker 000001,600036,AAPL")
        print("  • Or use the web interface: cd app && ./run.sh")
        
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")

if __name__ == "__main__":
    main()
