#!/usr/bin/env python3
"""
测试Gemini 1.5 Flash作为默认模型
使用免费的Gemini 1.5 Flash进行分析
"""

import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 已加载 .env 文件")
except ImportError:
    # 手动加载.env文件
    env_path = '.env'
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
        print("✅ 手动加载 .env 文件成功")

def test_gemini_flash_call():
    """测试Gemini 1.5 Flash调用"""
    print("\n🧪 测试Gemini 1.5 Flash调用")
    print("=" * 50)
    
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            print("❌ Google API密钥未设置")
            return False
        
        # 初始化Gemini 1.5 Flash模型（免费版本）
        model = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash-latest",
            api_key=api_key,
            temperature=0.1
        )
        
        print("✅ Gemini 1.5 Flash 初始化成功")
        
        # 简单测试调用
        response = model.invoke("请用一句话简单介绍苹果公司(AAPL)的投资价值。")
        print(f"🤖 Gemini 1.5 Flash 回答: {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ Gemini调用失败: {str(e)}")
        return False

def test_stock_analysis():
    """测试股票分析功能"""
    print("\n📊 测试股票分析功能")
    print("=" * 50)
    
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        
        api_key = os.getenv("GOOGLE_API_KEY")
        model = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash-latest",
            api_key=api_key,
            temperature=0.1
        )
        
        # 测试股票分析
        prompt = """
        请分析苹果公司(AAPL)的投资价值，包括：
        1. 公司基本面
        2. 财务状况
        3. 投资建议
        请用JSON格式回答：
        {
            "recommendation": "buy/sell/hold",
            "confidence": 85,
            "reasoning": "分析原因"
        }
        """
        
        response = model.invoke(prompt)
        print(f"📈 股票分析结果:")
        print(response.content)
        
        return True
        
    except Exception as e:
        print(f"❌ 股票分析测试失败: {str(e)}")
        return False

def test_chinese_stock_analysis():
    """测试中国股票分析"""
    print("\n🇨🇳 测试中国A股分析")
    print("=" * 50)
    
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        
        api_key = os.getenv("GOOGLE_API_KEY")
        model = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash-latest",
            api_key=api_key,
            temperature=0.1
        )
        
        # 测试中国股票分析
        prompt = """
        请分析平安银行(000001)的投资价值：
        1. 作为中国银行业的代表
        2. 财务表现和风险控制
        3. 投资建议
        请用中文回答，并给出买入/卖出/持有的建议。
        """
        
        response = model.invoke(prompt)
        print(f"🏦 中国A股分析结果:")
        print(response.content)
        
        return True
        
    except Exception as e:
        print(f"❌ 中国股票分析测试失败: {str(e)}")
        return False

def show_gemini_flash_advantages():
    """显示Gemini 1.5 Flash的优势"""
    print("\n🌟 Gemini 1.5 Flash 优势")
    print("=" * 60)
    
    advantages = [
        "🆓 **免费使用**: 有慷慨的免费配额",
        "⚡ **超快速度**: Flash版本专为速度优化",
        "💰 **成本最低**: 即使付费版本也非常便宜",
        "🌐 **多语言**: 对中英文都有很好的理解",
        "📊 **数据处理**: 擅长结构化数据分析",
        "🎯 **准确性**: 在大多数任务上表现优秀",
        "🔄 **高可用**: Google云基础设施保证",
        "📈 **适合金融**: 在金融分析任务上表现良好"
    ]
    
    for advantage in advantages:
        print(f"   {advantage}")

def show_usage_examples():
    """显示使用示例"""
    print("\n💡 Gemini 1.5 Flash 使用示例")
    print("=" * 60)
    
    examples = [
        {
            "description": "默认使用Gemini 1.5 Flash分析股票",
            "command": "python src/main.py --ticker AAPL",
            "note": "系统现在默认使用免费的Gemini 1.5 Flash"
        },
        {
            "description": "分析多只美股",
            "command": "python src/main.py --ticker AAPL,MSFT,GOOGL",
            "note": "快速分析多只股票，成本极低"
        },
        {
            "description": "分析中国A股",
            "command": "python src/main.py --ticker 000001,600036,000858",
            "note": "Gemini对中文理解很好，适合A股分析"
        },
        {
            "description": "大规模股票筛选",
            "command": "python src/main.py --ticker AAPL,MSFT,GOOGL,AMZN,TSLA",
            "note": "免费配额支持大量股票分析"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}️⃣ {example['description']}")
        print(f"   命令: {example['command']}")
        print(f"   说明: {example['note']}")

def main():
    """主测试函数"""
    print("🚀 Gemini 1.5 Flash 默认模型配置测试")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("🎯 目标: 使用免费的 Gemini 1.5 Flash 进行所有分析")
    
    # 运行测试
    tests_passed = 0
    total_tests = 3
    
    try:
        # 1. 测试基本调用
        if test_gemini_flash_call():
            tests_passed += 1
        
        # 2. 测试股票分析
        if test_stock_analysis():
            tests_passed += 1
        
        # 3. 测试中国股票分析
        if test_chinese_stock_analysis():
            tests_passed += 1
        
        # 显示优势
        show_gemini_flash_advantages()
        
        # 显示使用示例
        show_usage_examples()
        
        # 总结
        print(f"\n📊 测试总结")
        print("=" * 40)
        print(f"✅ 通过测试: {tests_passed}/{total_tests}")
        
        if tests_passed == total_tests:
            print("\n🎉 Gemini 1.5 Flash 配置完全成功!")
            print("\n✅ 系统现在将默认使用 Gemini 1.5 Flash")
            print("✅ 免费使用，无需担心配额问题")
            print("✅ 超快响应速度，适合实时分析")
            print("✅ 支持中美股票混合分析")
            print("✅ 成本最低的AI分析方案")
            
            print("\n🔧 下一步:")
            print("  1. 运行: python src/main.py --ticker AAPL")
            print("  2. 或启动Web界面: cd app && ./run.sh")
            print("  3. 享受免费高速的AI股票分析!")
            
            print("\n💡 提示:")
            print("  • Gemini 1.5 Flash 有慷慨的免费配额")
            print("  • 响应速度比GPT-4o和Claude都快")
            print("  • 特别适合大量股票筛选和日常分析")
            print("  • 如需更高质量分析，可随时切换到其他模型")
        else:
            print(f"\n⚠️  还有 {total_tests - tests_passed} 个测试未通过")
            print("请检查Google API密钥配置")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
