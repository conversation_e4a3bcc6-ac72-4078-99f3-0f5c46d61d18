#!/usr/bin/env python3
"""
模型版本管理脚本
用于查看、修改和管理AI模型配置
"""

import json
import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def load_models():
    """加载当前模型配置"""
    try:
        with open('src/llm/api_models.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载模型配置失败: {str(e)}")
        return []

def save_models(models):
    """保存模型配置"""
    try:
        with open('src/llm/api_models.json', 'w', encoding='utf-8') as f:
            json.dump(models, f, indent=2, ensure_ascii=False)
        print("✅ 模型配置已保存")
        return True
    except Exception as e:
        print(f"❌ 保存模型配置失败: {str(e)}")
        return False

def show_current_models():
    """显示当前模型配置"""
    print("📋 当前配置的模型")
    print("=" * 80)
    
    models = load_models()
    if not models:
        print("❌ 没有找到模型配置")
        return
    
    # 按提供商分组
    by_provider = {}
    for model in models:
        provider = model['provider']
        if provider not in by_provider:
            by_provider[provider] = []
        by_provider[provider].append(model)
    
    for provider, provider_models in by_provider.items():
        print(f"\n🏢 {provider} ({len(provider_models)} 个模型):")
        for i, model in enumerate(provider_models, 1):
            print(f"   {i:2d}. {model['display_name']:<25} → {model['model_name']}")
    
    print(f"\n📊 总计: {len(models)} 个模型")

def show_default_model():
    """显示默认模型配置"""
    print("\n🎯 默认模型配置")
    print("=" * 50)
    
    try:
        with open('src/utils/llm.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找默认模型配置
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'model_name = "' in line and 'if not model_name:' in lines[i-1]:
                model_name = line.split('"')[1]
                print(f"📊 默认模型: {model_name}")
            elif 'model_provider = "' in line and 'if not model_provider:' in lines[i-1]:
                provider = line.split('"')[1]
                print(f"📊 默认提供商: {provider}")
                
    except Exception as e:
        print(f"❌ 读取默认配置失败: {str(e)}")

def change_default_model():
    """修改默认模型"""
    print("\n⚙️ 修改默认模型")
    print("=" * 50)
    
    models = load_models()
    if not models:
        return
    
    # 显示可选模型
    print("可选模型:")
    for i, model in enumerate(models, 1):
        print(f"{i:2d}. {model['display_name']} ({model['provider']}) → {model['model_name']}")
    
    try:
        choice = input(f"\n请选择新的默认模型 (1-{len(models)}): ")
        choice_idx = int(choice) - 1
        
        if 0 <= choice_idx < len(models):
            selected_model = models[choice_idx]
            
            # 读取当前文件
            with open('src/utils/llm.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换默认配置
            lines = content.split('\n')
            new_lines = []
            
            for i, line in enumerate(lines):
                if 'model_name = "' in line and i > 0 and 'if not model_name:' in lines[i-1]:
                    new_lines.append(f'        model_name = "{selected_model["model_name"]}"')
                elif 'model_provider = "' in line and i > 0 and 'if not model_provider:' in lines[i-1]:
                    provider_map = {
                        "OpenAI": "OPENAI",
                        "Anthropic": "ANTHROPIC", 
                        "Gemini": "GEMINI",
                        "Groq": "GROQ",
                        "DeepSeek": "DEEPSEEK"
                    }
                    provider_code = provider_map.get(selected_model["provider"], "OPENAI")
                    new_lines.append(f'        model_provider = "{provider_code}"')
                else:
                    new_lines.append(line)
            
            # 保存文件
            with open('src/utils/llm.py', 'w', encoding='utf-8') as f:
                f.write('\n'.join(new_lines))
            
            print(f"✅ 默认模型已修改为: {selected_model['display_name']} ({selected_model['provider']})")
            
        else:
            print("❌ 无效的选择")
            
    except (ValueError, KeyboardInterrupt):
        print("❌ 操作已取消")

def add_custom_model():
    """添加自定义模型"""
    print("\n➕ 添加自定义模型")
    print("=" * 50)
    
    try:
        display_name = input("显示名称: ")
        model_name = input("模型名称: ")
        
        print("选择提供商:")
        providers = ["OpenAI", "Anthropic", "Gemini", "Groq", "DeepSeek"]
        for i, provider in enumerate(providers, 1):
            print(f"{i}. {provider}")
        
        provider_choice = int(input("提供商 (1-5): ")) - 1
        if 0 <= provider_choice < len(providers):
            provider = providers[provider_choice]
            
            # 加载现有模型
            models = load_models()
            
            # 添加新模型
            new_model = {
                "display_name": display_name,
                "model_name": model_name,
                "provider": provider
            }
            
            models.append(new_model)
            
            # 保存
            if save_models(models):
                print(f"✅ 已添加模型: {display_name} ({provider})")
            
        else:
            print("❌ 无效的提供商选择")
            
    except (ValueError, KeyboardInterrupt):
        print("❌ 操作已取消")

def remove_model():
    """删除模型"""
    print("\n🗑️ 删除模型")
    print("=" * 50)
    
    models = load_models()
    if not models:
        return
    
    # 显示模型列表
    for i, model in enumerate(models, 1):
        print(f"{i:2d}. {model['display_name']} ({model['provider']})")
    
    try:
        choice = input(f"\n请选择要删除的模型 (1-{len(models)}): ")
        choice_idx = int(choice) - 1
        
        if 0 <= choice_idx < len(models):
            removed_model = models.pop(choice_idx)
            
            if save_models(models):
                print(f"✅ 已删除模型: {removed_model['display_name']}")
        else:
            print("❌ 无效的选择")
            
    except (ValueError, KeyboardInterrupt):
        print("❌ 操作已取消")

def show_model_recommendations():
    """显示模型推荐配置"""
    print("\n💡 推荐模型配置")
    print("=" * 80)
    
    recommendations = [
        {
            "category": "🔥 最常用模型",
            "models": [
                ("GPT-4o", "gpt-4o", "OpenAI", "平衡性能和成本，适合日常使用"),
                ("Claude 3.5 Sonnet", "claude-3-5-sonnet-20241022", "Anthropic", "深度思考，质量最高"),
                ("Gemini 1.5 Flash", "gemini-1.5-flash-latest", "Gemini", "快速响应，成本低"),
                ("DeepSeek V3", "deepseek-chat", "DeepSeek", "中文支持好，成本低")
            ]
        },
        {
            "category": "💰 成本优化模型",
            "models": [
                ("GPT-4o Mini", "gpt-4o-mini", "OpenAI", "OpenAI最便宜的模型"),
                ("Claude 3.5 Haiku", "claude-3-5-haiku-latest", "Anthropic", "Claude最便宜的模型"),
                ("Gemini 1.5 Flash", "gemini-1.5-flash-latest", "Gemini", "Google最便宜的模型"),
                ("DeepSeek Chat", "deepseek-chat", "DeepSeek", "整体最便宜的模型")
            ]
        },
        {
            "category": "🎯 高质量模型",
            "models": [
                ("Claude 3.5 Sonnet", "claude-3-5-sonnet-20241022", "Anthropic", "最佳推理质量"),
                ("GPT-4o", "gpt-4o", "OpenAI", "平衡质量和速度"),
                ("o1 Preview", "o1-preview", "OpenAI", "复杂推理任务"),
                ("Gemini 1.5 Pro", "gemini-1.5-pro-latest", "Gemini", "多模态能力强")
            ]
        }
    ]
    
    for rec in recommendations:
        print(f"\n{rec['category']}:")
        for name, model_name, provider, desc in rec['models']:
            print(f"   • {name:<20} ({provider:<10}) → {desc}")

def main():
    """主菜单"""
    while True:
        print("\n🤖 AI模型版本管理")
        print("=" * 60)
        print("1. 查看当前模型配置")
        print("2. 查看默认模型")
        print("3. 修改默认模型")
        print("4. 添加自定义模型")
        print("5. 删除模型")
        print("6. 查看推荐配置")
        print("0. 退出")
        
        try:
            choice = input("\n请选择操作 (0-6): ")
            
            if choice == "1":
                show_current_models()
            elif choice == "2":
                show_default_model()
            elif choice == "3":
                change_default_model()
            elif choice == "4":
                add_custom_model()
            elif choice == "5":
                remove_model()
            elif choice == "6":
                show_model_recommendations()
            elif choice == "0":
                print("👋 再见!")
                break
            else:
                print("❌ 无效的选择，请重试")
                
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 操作失败: {str(e)}")

if __name__ == "__main__":
    main()
